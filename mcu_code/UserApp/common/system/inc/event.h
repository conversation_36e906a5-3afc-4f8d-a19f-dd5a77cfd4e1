/*
     event.h
描述：定义MCU内部和MCU与ARM侧交互的事件类型
     0x0000----0x0fff: IPC任务接收事件范围值
     0x1000----0x1fff: PM任务接收事件范围值
     0x2000----0x2fff: CAN任务接收事件范围值
     0x3000----0x3fff: Gsensor任务接收事件范围值
     0x4000----0x4fff: LED任务接收事件范围值
     0x5000----0x5fff: Bat任务接收事件范围值
     0x6000----0x6fff: 硬件诊断，按键，IO检测任务接收事件范围值
     0x7000----0x7fff: LOG任务接收事件范围值
     0x8000----0x8fff: UpDate任务接收事件范围值
     其他数据范围待保留
     0xf000----0xffff: 公共事件范围值(此事件同时被多个任务接收)
作者：廖勇刚
时间：2016.7.4
*/

#ifndef  _EVENT_H_
#define  _EVENT_H_

#include "NmStack_Types.h"
#include "SystemApi.h"

/************************宏定义***************************/
typedef unsigned char  UINT8;
typedef unsigned short UINT16;
typedef unsigned long  UINT32;
typedef signed   short INT16;

#define  TBOX_ECALL_STATUS                            0x00
#define  TBOX_TSP_STATUS                              0x01
#define  TBOX_SVT_STATUS                              0x02
#define  TBOX_CAN_NET_STATUS                          0x03
#define  TBOX_DIAG_STATUS                             0x04
#define  ARM_TYPE_CAN_CONTROL                         0x05
#define  ARM_TYPE_BLE_KEY_AUTH_STATUS                 0x06
#define  ARM_TYPE_UPGRADE_STATUS                      0x07
#define  TBOX_ARM_TYPE_FTM                            0xF0
#define  ARM_TYPE_WAKEUP_SRC_SYNC                     0xF1
#define  ARM_TYPE_MCU_LOG_PRINTF                      0xF2

//MCU硬件版本号
#define  MCU_HR_MAIN_VERSION                          0x01  // 五菱版本说明 1表示手工样;2表示OTS样;3表示NS样;4表示S样
#define  MCU_HR_SUB_VERSION                           0x00  // 0~99

/*外部软件版本信息*/
#define  TBOX_SOFT_MAIN_VERSION                       10    // T-BOX外部软件主版本号，次版本号为ARM侧更新，由DID存储 根据开沃客户更新
/*内部软件版本信息*/
#define  MCU_SOFT_MAIN_VERSION                        1     // MCU内部APP主版本号   五菱版本说明 1表示手工样;2表示OTS样;3表示NS样;4表示S样
#define  MCU_SOFT_SUB_VERSION                         10     // MCU内部APP次版本号   发布时修改  0~99
#define  MCU_SOFT_PATCH_VERSION                       0     // MCU内部APP补丁版本号  仅用于内部调试区分

//BLE配置   使用BLE不能使用debug Uart
#define  BLE_ENABLE                                   1


/*LOG相关配置*/
/*LOG配置模式*/
#define  LOG_NO_OUTPUT                          0x01 
#define  LOG_UART_OUTPUT                        0x02
#define  LOG_ARM_SAVE_OUTPUT                    0x03
#define  LOG_ARM_NO_SAVE_OUTPUT                 0x04

/*LOG配置等级*/
#define  LOG_INFO_OUTPUT                        (1 << 1)
#define  LOG_WARING_OUTPUT                      (1 << 2)
#define  LOG_ERROR_OUTPUT                       (1 << 3)
#define  LOG_ISR_OUTPUT                         (1 << 4)
#define  LOG_DEBUG_OUTPUT                       (1 << 5)


//LOG开关宏
#define  LOG_SWITCH_ON                          0x01
#define  LOG_SWITCH_OFF                         0x00
#define  LOG_SWITCH_CONFIG_DEBUG                LOG_SWITCH_OFF
#define  LOG_SWITCH_CONFIG_INFO                 LOG_SWITCH_ON
#define  LOG_SWITCH_CONFIG_WARING               LOG_SWITCH_ON
#define  LOG_SWITCH_CONFIG_ERROR                LOG_SWITCH_ON
#define  LOG_SWITCH_CONFIG_ISR                  LOG_SWITCH_OFF



#define  MASK_VALUE_IPC_TASK                    0x0   
#define  MASK_VALUE_PM_TASK                     0x1
#define  MASK_VALUE_CAN_TASK                    0x2
#define  MASK_VALUE_LED_TASK                    0x4
#define  MASK_VALUE_BAT_TASK                    0x5
#define  MASK_VALUE_DIAG_TASK                   0x6
#define  MASK_VALUE_LOG_TASK                    0x7
#define  MASK_VALUE_UPDATE_TASK                 0x8
#define  MASK_VALUE_BLE_TASK                    0x9
#define  MASK_VALUE_COMMON_TASK                 0xA
#define  MASK_VALUE_CANDUPL_TASK                0xB
#define  MASK_VALUE_COMMON_EVENT                0xF


//IPC任务接收的事件(0x0000---0x0fff)
#define  EVENT_ID_IPC_PERIOD_NOTIFY             0x0001


// 0X0200 --- 0x02FF    MCU----->ARM
#define  EVENT_ID_REQUEST_HEARTBEAT             0x0200


//0x0800 --- 0x08FF     ARM----->MCU 
#define  EVENT_ID_RESPONSE_HEARTBEAT            0x0800 


//PM电源管理任务接收的事件(0x1000----0x1fff)
#define  EVENT_ID_PM_PERIOD_NOTIFY              0x1001
#define  EVENT_ID_HEARTBEAT_TIMEOUT             0x1002
#define  EVENT_ID_MOVEMENT_STATUS               0x1003
#define  EVENT_ID_BPLUS_STATUS                  0x1004
#define  EVENT_ID_POWER_OFF                     0x1005
#define  EVENT_ID_PM_BATTERY_NOTIFY             0x1006




//CAN任务接收的事件(0x2000---0x2fff)
#define  EVENT_ID_CAN_PERIOD_NOTIFY             0x2001
#define  EVENT_ID_CAN_RX_DTC                    0x2002
#define  EVENT_ID_CAN_RX_DIAG_MSG               0x2003
#define  EVENT_ID_CAN_RX_IPC_REMOTE_CONTROL     0x2004


//Gsensor任务接收的事件(0x3000---0x3fff)
#define  EVENT_ID_GSENSOR_PERIOD_NOTIFY         0x3001




//LED任务接收的事件(0x4000---0x4fff)
#define  EVENT_ID_GPIO_CONTROL_PERIOD_NOTIFY    0x4001
#define  EVENT_ID_GPIO_HANDSHAKE_NOTIFY         0x4002
#define  EVENT_ID_ECALL_STATUS_NOTIFY           0x4003
#define  EVENT_ID_BCALL_STATUS_NOTIFY           0x4004



//Bat内置电池任务接收的事件(0x5000---0x5fff)
#define  EVENT_ID_BAT_PERIOD_NOTIFY             0x5001
#define  EVENT_ID_BAT_STATUS                    0x5005




//Diag and key 硬件诊断和按键以及IO检测任务的事件(0x6000---0x6fff)
#define  EVENT_ID_DIAG_PERIOD_NOTIFY            0x6001
#define  EVENT_ID_DIAG_DTC_EVENT                0x6002
#define  EVENT_ID_DIAG_MIC_EVENT                0x6003
#define  EVENT_ID_DIAG_HANDSHAKE_NOTIFY         0x6004




//Log任务接收的事件(0x7000---0x7fff)
#define  EVENT_ID_LOG_PERIOD_NOTIFY             0x7001


//UpDate 升级任务接收的事件(0x8000---0x8fff)
#define  EVENT_ID_UPDATA_PERIOD_NOTIFY          0x8001
#define  EVENT_ID_REMOTE_UPDATE_NOFIFY          0x8002
#define  EVENT_ID_RX_SUPPLIER_ID                0x8003
#define  EVENT_ID_RX_SPAREPARTNUMBER            0x8004
#define  EVENT_ID_RX_TBOX_ID                    0x8005
#define  EVENT_ID_RX_SIM_NUMBER                 0x8006
#define  EVENT_ID_RX_PCBA_SN                    0x8007

//bt 蓝牙任务接收的事件(0x9000---0x9fff)
#define  EVENT_ID_BT_PERIOD_NOTIFY              0x9001
#define  EVENT_ID_BT_RX_CMD                     0x9002  //接收到蓝牙模块命令
#define  EVENT_ID_BT_UPGRADE_CMD                0x9003  //接收到蓝牙模块升级命令
#define  EVENT_ID_BT_CLIENT_HANDSHAKE_NOTIFY    0x9004
//vin write event
#define  EVENT_ID_BT_VIN_WR_NOTIFY              0x9002


//common 任务公共事件,多个任务都接收此事件(0xf000---0xffff)
#define  EVENT_ID_HANDSHAKE_NOTIFY              0xf001
#define  EVENT_ID_ARM_STATUS                    0xf002  //电源任务  和 电池任务接收
#define  EVENT_ID_ACC_STATUS                    0xf003  //电源任务  和 电池任务接收
#define  EVENT_ID_MAINPOWER_STATUS              0xf004




#define  MCU_ADC_MAX_NUM                        0x08
#define  SIM_NUNBER_LEN                         13
#define  SIM_IMSI_LEN                           16
#define  SIM_ICCID_LEN                          20
#define  G4_MODULE_ID_LEN                       32
#define  GNSS_MODULE_ID_LEN                     32
#define  SPI_FRAME_LEN                          128
//每次传输日志太少，会占用过多的IPC资源，暂时改到300以上
#define  IPC_SEND_LOG_FRAME_LEN                 450 //130
#define  IPC_SEND_LOG_FRAME_MIN_LEN             300


/************************数据结构定义***************************/
typedef enum
{
    HANDSHAKE_FAIL   = 0,
    HANDSHAKE_SUCESS,
}HandShakeStatus;

typedef enum
{
    NETWORK_STATUS_NO_SIGNAL = 0,             //无信号
    NETWORK_STATUS_VERY_WEAK,                 //极弱
    NETWORK_STATUS_WEAK,                      //较弱
    NETWORK_STATUS_STRONG,                    //较强
    NETWORK_STATUS_VERY_STRONG,               //极强
} NetworkSignalStrength;

typedef enum  
{
    G4_GSM_NOT_REGISTERED = 0,
    G4_GSM_REGISTERED,
    G4_CONNECT_NETWORK,
}G4ModuleStatus;

typedef enum
{
    NAD_STATUS_NORAML = 0,                     //NAD 工作正常
    NAD_STATUS_ABNORAML,                       //NAD 工作异常
} NadWorkStatus;

typedef enum
{
    BPLUS_STATUS_LOST = 0,                     // B+ 掉电
    BPLUS_STATUS_PLUG,                         // B+ 恢复
} BplusStatus;

typedef enum
{
    BPLUS_VOLT_STATUS_NORMAL = 0,             // B+ 正常
    BPLUS_VOLT_STATUS_ABNORMAL,               // B+ 异常
} BplusVoltStatus;

typedef enum
{
    BAT_VOLT_STATUS_NORMAL = 0,            
    BAT_VOLT_STATUS_LOW,               
} BatVoltStatus;

typedef enum
{
    TSP_STATUS_INACTIVE = 0,
    TSP_STATUS_ACTIVE,
} TspStatus;

typedef enum
{
    ECALL_STATUS_INACTIVE = 0,                     
    ECALL_STATUS_ACTIVE,                          
} EcallStatus;

typedef enum
{
    SVT_STATUS_INACTIVE = 0,                     
    SVT_STATUS_ACTIVE,                          
} SvtStatus;


typedef enum
{
    CAR_NORAML_MOVEMENT = 0,                     
    CAR_ABNORAML_MOVEMENT,                          
} MoveStatus;



typedef enum
{
    UPDATE_CONDITION_NOT_MEET = 0,            // 升级条件不满足
    UPDATE_CONDITION_MEET,                    // 升级条件满足
}UpdateConditionStatus;


typedef enum
{
    MCU_MODE_RESET = 0,           // 复位模式启动
    MCU_MODE_LIGHT_SLEEP,         // 轻度睡眠唤醒启动
    MCU_MODE_DEEP_SLEEP,          // 深度睡眠唤醒启动
    MCU_MODE_BACKUP_SLEEP,        // 备电睡眠唤醒启动
} McuModeStatus;

typedef enum
{
    TBOX_WAKEUP_BPLUS = 0,        // KL30 上电复位
    TBOX_WAKEUP_ACC,              // ACC 唤醒
    TBOX_WAKEUP_LPS,              // LPS 唤醒
    TBOX_WAKEUP_RTC,              // RTC 唤醒
    TBOX_WAKEUP_CAN1,             // CAN1 唤醒
    TBOX_WAKEUP_CAN2,             // CAN2 唤醒
    TBOX_WAKEUP_LIN,              // LIN 唤醒
    TBOX_WAKEUP_CAN3,             // CAN3 唤醒
    TBOX_WAKEUP_CAN4,             // CAN4 唤醒
    TBOX_WAKEUP_MODULE,           // 4G模块唤醒
    TBOX_WAKEUP_WATCHDOE,         // 看门狗复位唤醒
    TBOX_WAKEUP_EXTERN_RESET,     // 复位管脚唤醒
    TBOX_WAKEUP_SOFT_RESET,       // 软件复位唤醒
    TBOX_WAKEUP_UNKNOWN,          // 未知唤醒
    TBOX_WAKEUP_SENSOR,           // SENSOR 唤醒
    TBOX_WAKEUP_BLE,              // 蓝牙唤醒
    TBOX_WAKEUP_ECALL,            // ECALL 唤醒
    TBOX_WAKEUP_FCHG,             // 快充 唤醒
    TBOX_WAKEUP_SCHG,             // 慢充 唤醒
    TBOX_WAKEUP_SMS = TBOX_WAKEUP_MODULE + 10, //短信唤醒
    TBOX_WAKEUP_PHONECALL = TBOX_WAKEUP_MODULE + 11, //电话唤醒
} TboxWakeupSource;


typedef enum
{
    ECU_CONDICATION_NOT_MEET = 0,
    ECU_CONDICATION_MEET,
}EcuCondicationStatus;

typedef enum
{
    ECU_REMOTE_UPDATE_IDLE = 0,
    ECU_REMOTE_UPDATE_BUSY,
} EcuRemoteUpdateStatus;

typedef enum
{
    UPDATE_TYPE_MCU = 0,            
    UPDATE_TYPE_ECU,
    UPDATE_TYPE_BT,
} UpdateType;

typedef enum
{
    UPDATE_STATUS_START = 0,
    UPDATE_STATUS_END,
    UPDATE_STATUS_HARD_RESET,
    UPDATE_STATUS_WRITE_FINGER,
    UPDATE_STATUS_CHECK_DEPENDENCY,
    UPDATE_STATUS_SOFT_RESET,
    UPDATE_STATUS_MAX,
} UpdateStatus;

typedef enum
{
    SPI_STATUS_IDLE = 0,
    SPI_STATUS_RX_DATA,
    SPI_STATUS_RX_TEST,
    SPI_STATUS_TX_LOG,
    SPI_STATUS_TX_CAN,
}SpiStatus;

typedef enum
{
    GNSS_MODULE_NOT_FIX = 0,
    GNSS_MODULE_FIX,
    GNSS_MODULE_SERIAL_FAULT,
}GnssFixStatus;


typedef enum  
{
    SIM_NOT_RECOGNIZED = 0,
    SIM_RECOGNIZED,
}SimStatus;

typedef enum
{
    CAN_RX_IDLE = 0,
    CAN_RX_STATUS,
}CanRxStatus;

typedef enum
{
    ALREADY_IN = 0,
    NOT_IN,
}InterruptStatus;


typedef enum
{
    POWER_ON_REPORT_IDLE    = 0,
    POWER_ON_REPORT_WAKEUP_STATUS,
    POWER_ON_REPORT_CAR_INFO,
    POWER_ON_REPORT_REQ_DEVICE_UPDATE,
    POWER_ON_CAN_NET_STATUS,
    POWER_ON_REPORT_HARD_CONFIG,
    POWER_ON_REPORT_ENTER_FTM,
}PowerOnReportStatus;



typedef enum
{
    MCU_FAULT_TYPE_BPLUS_LOW = 0,   /*低电类型状态通知*/
    MCU_FAULT_BURGLAR_ALARM,        /*防盗报警类型通知*/
    MCU_VEHILCE_BLE_KEY_REQUEST,    /*开沃项目蓝牙钥匙状态请求*/
    MCU_FAULT_AIRBAG,               /*安全气囊类型通知*/
    MCU_FAULT_IG_STATUS = 0x06,     /*车辆IG点火状态通知*/
}McuFaultType;

typedef enum
{
    MCU_FAULT_INACTIVE = 0,
    MCU_FAULT_ACTIVE,
}McuFaultStatus;

typedef enum
{
    ARM_NULL = 0,
    ARM_REMOTE_CTRL,
    ARM_REMOTE_DIAG,
    ARM_REMOTE_OTAMODE,
    ARM_REMOTE_OTANETWORK,
} ArmWorkSource;    //目前用于表示网络唤醒源，远控或远程诊断，以后有需求可覆盖到其他控制

typedef enum
{
    WORK_STATUS_INACTIVE = 0x00,
    WORK_STATUS_ACTIVE,
    WORK_STATUS_UNINITIALIZED,
} WorkStatus;


typedef enum
{
    ARM_CLINET_OFFLINE = 0,
    ARM_CLINET_ONLINE,
}ArmClinetStatus;

typedef enum
{
    GNSS_IS_ONLINE = 0,
    GNSS_IS_OFFLINE,
}GnssOnlineStatus;


typedef struct updateFrameInfo
{
    UINT16 curLen;                           //当前接收长度
    UINT8  data[SPI_FRAME_LEN];              //消息体
}UpdateFrameInfo;

typedef struct ecuUpdateInfo
{
    UINT8  id;
    UINT8  rxStatus;
    UINT8  idType;
}EcuUpdateInfo;

typedef enum
{
    CAN_SEND_OUT_ENABLE = 0,
    CAN_SEND_OUT_DISABLE,
}CanSendOutStat;

//zh
typedef enum
{
    BT_PIN_RESULT_NOT_OK = 0,
    BT_PIN_RESULT_OK,
}BtPinResult;
//zh

typedef enum
{
    TSP_STATUS_RESISTER_UNKNOWN = 0, //未知状态
    TSP_STATUS_UNREGISTERED,         //未注册
    TSP_STATUS_REGISTERED,           //已注册
    TSP_STATUS_UNLOGINED,            //未登录
    TSP_STATUS_LOGINED,              //已登录
}ArmTspStatus;

typedef enum
{
    BT_STATUS_INACTIVE = 0,                     
    BT_STATUS_ACTIVE,  
    BT_STATUS_IDLE,
} BtStatus;


// 定义掩码
#define MASK_NULL         0x00  // 0000 0000
#define MASK_VOL          0x01  // 0000 0001
#define MASK_ACC          0x02  // 0000 0010
#define MASK_LOW_POWER    0x04  // 0000 0100
#define MASK_BUS_OFF      0x08  // 0000 1000
typedef union {
    struct {
        UINT8 vol : 1; //电压正常使能
        UINT8 acc : 1; //有acc使能
        UINT8 lowPowerMode : 1; //为0x02(ON)使能
        UINT8 busOff : 1; //无busOff使能
        UINT8 bit4 : 1;
        UINT8 bit5 : 1;
        UINT8 bit6 : 1;
        UINT8 stop : 1; //停止检测标志
    } bits;
    UINT8 value;   // 整个8位数据
} DtcEnable;

typedef struct
{
    WorkStatus             accStatus;                           //ACC状态          AccStatus
    WorkStatus             canNetRequestStatus;                 //CAN网络请求状态
    WorkStatus             remoteControlStatus;                 //远程控制状态
    WorkStatus             diagStatus;                          //诊断状态
    WorkStatus             tspStatus;                           //TSP活动状态 模组获取状态       TspStatus
    WorkStatus             canStatus;                           //CAN活动状态        CanStatus

    GnssOnlineStatus       gnssIsOnline;                        //惯导模块故障状态
    UINT8                  handshakeStatus;                     //握手状态           HandShakeStatus
    UINT8                  bplusStatus;                         // B+状态            BplusStatus
    UINT8                  bplusVoltStatus;                     // B+低电状态        BplusVoltStatus
    UINT8                  batVoltStatus;                       // 内置电池状态      BatVoltStatus
    UINT8                  updateConditionStatus;               //升级条件状态       UpdateConditionStatus
    UINT8                  emmcStatus;                          //EMMC状态
    UINT8                  ethStatus;                           //以太网状态
    BtStatus               btStatus;                            //BT活动状态 
    BtPinResult            btPinResult;                         //BT PIN认证结果
    ArmTspStatus           tspConnectStatus;                   //TSP注册/登录状态
    UINT8                  btMac[6];                            //TBOX板载蓝牙
    WorkStatus             vinCodeStatus;                       //VIN码是否有效状态
    UINT16                 bPlusAdcValueFilter;                 //B+ 电压 滤波后

    UINT8                  ecallStatus;                         //ECALL激活状态      EcallStatus
    UINT8                  svtStatus;                           //SVT激活状态        SvtStatus
    UINT16                 bPplusAdcValue;                      //B+ 电压
    UINT16                 batVoltAdcValue;                     //电池电压ADC值
    UINT16                 batTempAdcValue;                     //电池温度ADC值
    UINT16                 eCallAdcValue;                       //ecall ADC值
    UINT16                 analog1AdcValue;                     //ANALOG1 ADC值
    UINT16                 analog2AdcValue;                     //ANALOG2 ADC值
    UpdateFrameInfo        updateFrame;                         //升级帧信息
    UINT8                  spiStatus;                           //SPI接收测试状态    SpiStatus
    UINT8                  ecuRemoteUpdateStatus;               //ECU远程升级状态    EcuRemoteUpdateStatus
    EcuUpdateInfo          ecuUpdateInfo;                       //ECU设备和升级文件类型
    UINT8                  simNo[SIM_NUNBER_LEN];               //SIM卡号码
    UINT8                  simImsi[SIM_IMSI_LEN];               //SIM卡IMSI
    UINT8                  simIccid[SIM_ICCID_LEN];             //SIM卡ICCID
    UINT8                  g4Id[G4_MODULE_ID_LEN];              //G4 MODULE ID
    UINT8                  gnssId[GNSS_MODULE_ID_LEN];          //GNSS MODULE ID 
    UINT8                  canRxStatus;                         // CanRxStatus
    UINT8                  canIdleCount;
    UINT8                  canReportInit;
    UINT16                 micVoltAdcValue;                     //mic故障采样值
    UINT8                  powerOnReportStatus;                 // PowerOnReportStatus
    UINT8                  heartFailCount;
    UINT8                  armFailCount;
    UINT8                  intoInterrupt;                       //判断是否已经进入ADC检测
    UINT16                 ftmErrorCount;                       //装备接收错误计数
    UINT32                 canTotalNum;                         //can接收总包数
    UINT32                 canLostNum;                          //can丢帧总包数
    ArmClinetStatus        armClinetStatus;                     //arm 客户端连接状态
    UINT8                  armClientCount;                      //arm 客户端连接计数
    CanSendOutStat         canSendOutStat;                      /*是否允许can往外发送can报文*/
    Nm_StateType           Nm_State;                            /*can 网络当前状态*/
    UINT8                  udsCtrlNmPduStatus;                  /*诊断协议控制网络报文收发状态*/
    UINT8                  udsCtrlNormPduStatus;                 /*诊断协议控制应用报文收发状态*/
    UINT8                  mainPowerLowSleep;
    UINT8                  bleKeyAuthStatus;                    /*蓝牙钥匙鉴权认证状态。 1： 认证成功， 0: 认证失败*/

    //ARM 状态
    UINT8                  networkSignalStrength;               //网络信号强度
    UINT8                  gpsSatelliteCount;                   //GPS卫星个数
    UINT16                 gpsDirection;                        //GPS方向
    UINT8                  nadWorkStatus;                       //NAD 工作状态      NadWorkStatus
    UINT8                  wifiSignalStrength;                  //WIFI信号强度      NetworkSignalStrength
    UINT8                  wifiStatus;                          //WIFI状态          WifiStatus
    UINT8                  g4ModuleStatus;                      // 4G模块状态      G4ModuleStatus
    UINT8                  gnssSignalStrength;                  //GNSS信号强度
    UINT8                  gnssFixStatus;                       //GNSS定位状态       GnssFixStatus
    UINT32                 gnssLatitude;                        //GNSS纬度 /1000000 = dddmm.mmmmmmm 0~90 为北纬，90~180 为南纬
    UINT32                 gnssLongitude;                       //GNSS经度 /1000000 = dddmm.mmmmmmm 0~180 为东经，180~360 为西经
    UINT32                 gnssSpeed;                           //GNSS速度 /1000 = 1.852km/h
    UINT8                  simStatus;                           // sim卡状态       SimStatus

    UINT8                  RemoteUpgradeFlag;                   /*远程诊断升级标志*/
    UINT8                  upgradeStatus;                       /*升级状态*/

    UINT8                  fchgStatus;                          /*快充电枪状态*/
    UINT8                  schgStatus;                          /*慢充电枪状态*/

    DtcEnable              dtcEnable;                       /*dtc检查的使能标志，acc上电2秒后使能*/
}CommonInfo;

#pragma pack(1)
typedef struct backupRamInfo
{
    UINT8            mcuModeStatus;
    UINT8            mcuWakeupSource;
    UINT8            lightSleepCount;
    UINT32           lastResetARMTime;  //时间搓
    UINT8            resever[25];       /*T-Box的NV空间划分,固定32.*/
}BackupRamInfo;
#pragma pack()


#endif

