/*
      LedApi.c
描述：此文件主要是LED任务具体实现的业务功能，待实现
作者：廖勇刚
时间：2016.7.5
*/
#include "LedApi.h"
#include "AppTask.h"
#include "BatApi.h"
#include "CanFtm.h"
#include "IpcApi.h"
#include "LogApi.h"
#include "NvApi.h"
#include "PmApi.h"
#include "event.h"
#include "gpio.h"
#include "pwm.h"
#include "r_can.h"

/************************外部全局变量****************************/
extern GpioInfo g_gpioPowerOnInfoList[];
extern CommonInfo g_commonInfo;
extern IpcInfo g_ipcInfo;

/************************函数接口***************************/

/************************全局变量****************************/
GpioControlInfoStruct g_GpioControlInfoStruct[GPIO_CONTROL_ENUM_MAX];
ModulePowerStatus  g_powerModuleStatus = MODULE_POWER_IDLE;

static const EventInfo g_ledEventFunctionMap[] = 
{
//    {EVENT_ID_GPIO_HANDSHAKE_NOTIFY,       GpioControlHandShakeFunction},
//    {EVENT_ID_ECALL_STATUS_NOTIFY,         ECallStatusNotifyFunction},
};

ModulePowerStatus GetPowerModuleStatus(void)
{
    return g_powerModuleStatus;
}
/*************************************************
函数名称: ARMWorkModeControl
函数功能: ARM开关机复位唤醒控制
输入参数: 无
输出参数: 无
函数返回类型值：无
编写者: heyuhui
编写日期 :2025/02/21
*************************************************/
void ARMWorkModeControl(void)
{
    static uint8_t count = 0;
    uint8_t  tempBuf[1] = {0x00};
    Msg    msg;

    switch(g_powerModuleStatus)
    {
        case MODULE_POWER_IDLE:
        {
            break;
        }
        
        case MODULE_POWER_ON_GPRS_PWR_EN_LOW:
        {
            count++;
            GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_ARM_RESET], GPIO_OUTPUT_HIGH);
            if(MODULE_RESET_PKON_INTERVAL <= count)
            {
                GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_ARM_RESET], GPIO_OUTPUT_LOW);
                g_powerModuleStatus = MODULE_POWER_ON_ONOFF_HIGH;
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "mcu->arm reset success\r\n");
                count = 0;
            }
            break;
        }
        
        case MODULE_POWER_ON_ONOFF_HIGH:
        {
            count++;
            if (MODULE_RESET_PKON_INTERVAL <= count)
            {
                GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_M_PWR_ONOFF], GPIO_OUTPUT_HIGH);
                g_powerModuleStatus = MODULE_POWER_ON_ONOFF_LOW;
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "mcu->arm pwr_on press\r\n");
                count = 0;
            }
            break;
        }
        
        case MODULE_POWER_ON_ONOFF_LOW:
        {
            count++;
            if(MODULE_POWER_ON_TIME <= count)
            {
                GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_M_PWR_ONOFF], GPIO_OUTPUT_LOW);
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "mcu->arm pwr_on release\r\n");
                g_powerModuleStatus = MODULE_POWER_ON_TRANSLATOR_EN_LOW;
                count = 0;
            }
            break; 
        }
        
        case MODULE_POWER_ON_TRANSLATOR_EN_LOW:
        {
            GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_M_TRANSLATOR_EN], GPIO_OUTPUT_LOW);
            uint16_t bPplusValue = ReadBpPlusValue();
            g_powerModuleStatus = MODULE_POWER_ON_UART_INIT;
            #if(LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "mcu plus adc is %d\r\n", bPplusValue);
            #endif
            break;
        }
        case MODULE_POWER_ON_UART_INIT:
        {
            count++;
            if(250 <= count) //25s IPC通信正常
            {
                count = 0;
                g_powerModuleStatus = MODULE_POWER_IDLE;
            }
            break;
        }
        case MODULE_POWER_OFF_TRANSLATOR_EN_HIGH:
        {
            count++;
            if(0x01 == count)
            {
                tempBuf[0] = PM_STATUS_POWER_OFF;
                msg.event = MESSAGE_TX_PM_REQUEST;
                msg.len   = 1;
                msg.lparam = (uint32_t)&tempBuf[0];
                SystemSendMessage(TASK_ID_IPC, msg);
            }
            else if(0x09 <= count)
            {
                count = 0x00;
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "power off arm\r\n");
                GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_M_TRANSLATOR_EN], GPIO_OUTPUT_HIGH);
                g_powerModuleStatus = MODULE_POWER_OFF_ONOFF_HIGH;
            }
            break;
        }
        
        case MODULE_POWER_OFF_ONOFF_HIGH:
        {
            GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_M_PWR_ONOFF], GPIO_OUTPUT_HIGH);
            g_powerModuleStatus = MODULE_POWER_OFF_ONOFF_LOW;
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "mcu->arm pwr_off start\r\n");
            count = 0;
            break;
        }
        case MODULE_POWER_OFF_ONOFF_LOW:
        {
            count++;
            if(MODULE_POWER_OFF_TIME == count)
            {
                GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_M_PWR_ONOFF], GPIO_OUTPUT_LOW);
                g_powerModuleStatus = MODULE_POWER_OFF_GPRS_PWR_EN_HIGH;
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "mcu->arm pwr_off success\r\n");
                count = 0;
            }
            break; 
        }
        
        case MODULE_POWER_OFF_GPRS_PWR_EN_HIGH:
        {
            GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_4V1_PWR_SW], GPIO_OUTPUT_HIGH);
            g_powerModuleStatus = MODULE_POWER_IDLE;
            break;
        }
        case MODULE_POWER_WAKEUP_ARM_HIGH:
        {
            GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_WAKEUP_ARM], GPIO_OUTPUT_HIGH);
            count ++;
            if(MODULE_WAKE_UP_LASTING_TIME == count)
            {
                g_powerModuleStatus = MODULE_POWER_WAKEUP_ARM_LOW;
                #if(LOG_SWITCH_CONFIG_INFO == LOG_SWITCH_ON)
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "mcu wakeup arm\r\n");
                #endif
                //to recover the count for use by other cases
                count = 0;
            }
            break;
        }
        case MODULE_POWER_WAKEUP_ARM_LOW:
        {
            GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_WAKEUP_ARM], GPIO_OUTPUT_LOW);
            g_powerModuleStatus = MODULE_POWER_IDLE;
            break;
        }
        case MODULE_RESET_LOW:
        {
            count++;
            if(1 == count)
            {
                GpioSetOutputLevel(&g_gpioPowerOnInfoList[MCU_GPIO_M_TRANSLATOR_EN], GPIO_OUTPUT_HIGH);
            }
            else if(30 < count) //3s
            {
                count = 0;
                g_powerModuleStatus = MODULE_POWER_ON_GPRS_PWR_EN_LOW;
                SystemApiLogPrintf(LOG_INFO_OUTPUT, "mcu reset arm start\r\n");
            }
            break;
        }
        default:
        {
            break;
        }
    }
}

/*************************************************
函数名称: GpioLedControlFirstInit
函数功能: Mcu led初始配置
输入参数: 无
输出参数: 无
函数返回类型值： 
编写者: lfc
编写日期 :2025/01/16
*************************************************/
int GpioLedControlFirstInit(void)
{
    //设置LED控制对应的GPIO
    g_GpioControlInfoStruct[GPIO_CONTROL_CAN_RED_LED].ioRemap   = MCU_GPIO_HANDSHAKE_LED_RED;
    g_GpioControlInfoStruct[GPIO_CONTROL_CAN_GREEN_LED].ioRemap = MCU_GPIO_HANDSHAKE_LED_GREEN;
    g_GpioControlInfoStruct[GPIO_CONTROL_MODULE_RED_LED].ioRemap      = MCU_GPIO_4G_LED_RED;
    g_GpioControlInfoStruct[GPIO_CONTROL_MODULE_GREEN_LED].ioRemap    = MCU_GPIO_4G_LED_GREEN;
    g_GpioControlInfoStruct[GPIO_CONTROL_GNSS_RED_LED].ioRemap        = MCU_GPIO_GPS_LED_RED;
    g_GpioControlInfoStruct[GPIO_CONTROL_GNSS_GREEN_LED].ioRemap      = MCU_GPIO_GPS_LED_GREEN;
    g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED].ioRemap = MCU_GPIO_M_SOS_LED_GREEN;
    g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_RED_LED].ioRemap   = MCU_GPIO_M_SOS_LED_RED;

    //添加对应的 GpioCtrlTypeEnum
    g_GpioControlInfoStruct[GPIO_CONTROL_CAN_RED_LED].pairedCtrlType   = GPIO_CONTROL_CAN_GREEN_LED;
    g_GpioControlInfoStruct[GPIO_CONTROL_CAN_GREEN_LED].pairedCtrlType = GPIO_CONTROL_CAN_RED_LED;
    g_GpioControlInfoStruct[GPIO_CONTROL_MODULE_RED_LED].pairedCtrlType      = GPIO_CONTROL_MODULE_GREEN_LED;
    g_GpioControlInfoStruct[GPIO_CONTROL_MODULE_GREEN_LED].pairedCtrlType    = GPIO_CONTROL_MODULE_RED_LED;
    g_GpioControlInfoStruct[GPIO_CONTROL_GNSS_RED_LED].pairedCtrlType        = GPIO_CONTROL_GNSS_GREEN_LED;
    g_GpioControlInfoStruct[GPIO_CONTROL_GNSS_GREEN_LED].pairedCtrlType      = GPIO_CONTROL_GNSS_RED_LED;
    g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED].pairedCtrlType = GPIO_CONTROL_ECALL_SOS_RED_LED;
    g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_RED_LED].pairedCtrlType   = GPIO_CONTROL_ECALL_SOS_GREEN_LED;

    for(int index = GPIO_CONTROL_CAN_RED_LED; index < GPIO_CONTROL_ENUM_MAX; index++)
    {
        g_GpioControlInfoStruct[index].curStatus = GPIO_STATUS_OFF;
        g_GpioControlInfoStruct[index].preStatus = GPIO_STATUS_OFF;
        g_GpioControlInfoStruct[index].ioSetTime = 0x00;
        g_GpioControlInfoStruct[index].ioResetTime = 0x00;
        g_GpioControlInfoStruct[index].ioTimeRecord = 0x00;
        PwmChannelStop(g_GpioControlInfoStruct[index].ioRemap);
    }

    //设置LED初始状态 主要是亮灯状态
    GpioLedControlUpdateStatusFunction(GPIO_CONTROL_CAN_GREEN_LED, GPIO_STATUS_ON,
                                       0, 0);
    GpioLedControlUpdateStatusFunction(GPIO_CONTROL_GNSS_GREEN_LED, GPIO_STATUS_BLINKED,
                                       GPIO_GNSS_LED_GREEN_SET_TIME, GPIO_GNSS_LED_GREEN_RESET_TIME);
    GpioLedControlUpdateStatusFunction(GPIO_CONTROL_MODULE_GREEN_LED, GPIO_STATUS_BLINKED,
                                       GPIO_MODULE_LED_GREEN_SET_TIME, GPIO_MODULE_LED_GREEN_RESET_TIME);
                          
    return 0;
}

void ControlGpsLed(void)
{
    bool gpsFault = GetDtcFaultStatus(DTC_GNSS_MODULE_FAULT_INDEX);
    bool gpsAntennaOpen = GetDtcFaultStatus(DTC_GNSS_ANT_OPEN_INDEX);
    bool gpsAntennaShort = GetDtcFaultStatus(DTC_GNSS_ANT_SHORT_INDEX);
    bool gpsAntennaFault = gpsAntennaOpen || gpsAntennaShort;

    GpioCtrlTypeEnum ledType = GPIO_CONTROL_GNSS_GREEN_LED;
    GpioStatusTypeEnum ledStatus = GPIO_STATUS_ON;
    uint8_t setTime = 0, resetTime = 0;

    SystemApiLogPrintf(LOG_INFO_OUTPUT, "GPS LED: gnssFixStatus=%d, gpsFault=%d, antOpen=%d, antShort=%d\r\n",
                       g_commonInfo.gnssFixStatus, gpsFault, gpsAntennaOpen, gpsAntennaShort);

    // 临时调试：显示天线故障但不影响LED显示（仅用于测试）
    // 注意：生产环境中应该移除此注释，保持天线故障检测
    // gpsAntennaFault = false;  // 取消注释此行可临时忽略天线故障

    if (gpsFault)
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "GPS LED: GNSS module fault - red on\r\n");
        ledType = GPIO_CONTROL_GNSS_RED_LED;
        ledStatus = GPIO_STATUS_ON;
    }
    else if(gpsAntennaFault)
    {
        if (gpsAntennaOpen) {
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "GPS LED: GNSS antenna open fault - red blink\r\n");
        }
        if (gpsAntennaShort) {
            SystemApiLogPrintf(LOG_INFO_OUTPUT, "GPS LED: GNSS antenna short fault - red blink\r\n");
        }
        ledType = GPIO_CONTROL_GNSS_RED_LED;
        ledStatus = GPIO_STATUS_BLINKED;
        setTime = GPIO_GNSS_LED_RED_SET_TIME;
        resetTime = GPIO_GNSS_LED_RED_RESET_TIME;
    }
    else if(g_commonInfo.gnssFixStatus == GNSS_MODULE_FIX)
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "GPS LED: GNSS fix successful - green on\r\n");
        ledType = GPIO_CONTROL_GNSS_GREEN_LED;
        ledStatus = GPIO_STATUS_ON;
    }
    else
    {
        SystemApiLogPrintf(LOG_INFO_OUTPUT, "GPS LED: GNSS searching/positioning - green blink (fixStatus=%d)\r\n", g_commonInfo.gnssFixStatus);
        ledType = GPIO_CONTROL_GNSS_GREEN_LED;
        ledStatus = GPIO_STATUS_BLINKED;
        setTime = GPIO_GNSS_LED_GREEN_SET_TIME;
        resetTime = GPIO_GNSS_LED_GREEN_RESET_TIME;
    }

    GpioLedControlUpdateStatusFunction(ledType, ledStatus, setTime, resetTime);
}

void ControlCanBusLed(void)
{
    // 当任意一路有故障时，设置为红灯
    if (CheckMcuCanBusOffStatus())
    {
        GpioLedControlUpdateStatusFunction(GPIO_CONTROL_CAN_RED_LED, GPIO_STATUS_ON, 0, 0);
    }
    else
    {
        GpioLedControlUpdateStatusFunction(GPIO_CONTROL_CAN_GREEN_LED, GPIO_STATUS_ON, 0, 0);
    }
}

void Control4GLed(void)
{
    // 4G 模块状态判断
    GpioCtrlTypeEnum ledType = GPIO_CONTROL_MODULE_RED_LED;
    GpioStatusTypeEnum ledStatus = GPIO_STATUS_ON;
    uint8_t setTime = 0, resetTime = 0;

    // 检查网络模块故障
    if (GetDtcFaultStatus(DTC_NETWORK_MODULE_FAILURE_INDEX)) // 红灯常亮：网络模块故障
    {
        ledType = GPIO_CONTROL_MODULE_RED_LED;
        ledStatus = GPIO_STATUS_ON;
    }
    else if (!g_commonInfo.simStatus) // 红灯闪烁：SIM 未检测到
    {
        ledType = GPIO_CONTROL_MODULE_RED_LED;
        ledStatus = GPIO_STATUS_BLINKED;
        setTime = GPIO_MODULE_LED_RED_SET_TIME;
        resetTime = GPIO_MODULE_LED_RED_RESET_TIME;
    }
    else // 根据功能规范，绿灯的情况一定检测到了SIM卡
    {
        switch (g_commonInfo.g4ModuleStatus)
        {
            case G4_GSM_NOT_REGISTERED:
            case G4_GSM_REGISTERED:
            {
                ledType = GPIO_CONTROL_MODULE_RED_LED;     // 红灯闪烁：GSM驻网
                ledStatus = GPIO_STATUS_BLINKED;
                setTime = GPIO_MODULE_LED_RED_SET_TIME;
                resetTime = GPIO_MODULE_LED_RED_RESET_TIME;
                break;
            }
            case G4_CONNECT_NETWORK:
            {
                ledType = GPIO_CONTROL_MODULE_GREEN_LED;   // 绿灯常亮：GSM驻网且网络PING成功
                ledStatus = GPIO_STATUS_ON;
                break;
            }
            default:
            {
                return;
            }
        }
    }
    // 更新 LED 状态
    GpioLedControlUpdateStatusFunction(ledType, ledStatus, setTime, resetTime);
}


/*************************************************
函数名称: GpioLedControlHandle
函数功能: LED显示处理函数
输入参数: 无
输出参数: 无
函数返回类型值： 
编写者: lfc
编写日期 :2025/01/16
*************************************************/
void GpioLedControlHandle(void)
{
    FtmInfo *pFtmInfo = FtmInitRead();

    if(FTM_MODE_EXIT == pFtmInfo->ftmMode)
    {
        ControlGpsLed();
        ControlCanBusLed();
        Control4GLed();
    }
}
/*************************************************
函数名称: LedBlinkedControlHandle
函数功能: LED显示处理函数
输入参数: 无
输出参数: 无
函数返回类型值：
编写者: lfc
编写日期 :2025/01/16
*************************************************/
void LedBlinkedControlHandle(void)
{
    for (int index = GPIO_CONTROL_CAN_RED_LED; index < GPIO_CONTROL_ENUM_MAX; index++)
    {
        switch (g_GpioControlInfoStruct[index].curStatus)
        {
            case GPIO_STATUS_BLINKED:
            {
                // 间隔闪烁状态处理
                if (g_GpioControlInfoStruct[index].preStatus == GPIO_STATUS_ON)
                {
                    if (g_GpioControlInfoStruct[index].ioResetTime == g_GpioControlInfoStruct[index].ioTimeRecord)
                    {
                        PwmChannelStop(g_GpioControlInfoStruct[index].ioRemap);
                        g_GpioControlInfoStruct[index].preStatus = GPIO_STATUS_OFF;
                        g_GpioControlInfoStruct[index].ioTimeRecord = 0;
                    }
                    else
                    {
                        g_GpioControlInfoStruct[index].ioTimeRecord++;
                    }
                }
                else
                {
                    if (g_GpioControlInfoStruct[index].ioSetTime == g_GpioControlInfoStruct[index].ioTimeRecord)
                    {
                        PwmChannelStart(g_GpioControlInfoStruct[index].ioRemap);
                        g_GpioControlInfoStruct[index].preStatus = GPIO_STATUS_ON;
                        g_GpioControlInfoStruct[index].ioTimeRecord = 0;
                    }
                    else
                    {
                        g_GpioControlInfoStruct[index].ioTimeRecord++;
                    }
                }
                break;
            }

            case GPIO_STATUS_FLASH:
            {
                // 闪烁操作
                if (g_GpioControlInfoStruct[index].ioSetTime == g_GpioControlInfoStruct[index].ioTimeRecord)
                {
                    PwmChannelStart(g_GpioControlInfoStruct[index].ioRemap);
                    PwmChannelStop(g_GpioControlInfoStruct[index].ioRemap);
                    g_GpioControlInfoStruct[index].ioTimeRecord = 0;
                }
                else
                {
                    g_GpioControlInfoStruct[index].ioTimeRecord++;
                }
                break;
            }

            default:
                break;
        }
    }
}


/*************************************************
函数名称: GpioControlPeriodNotifyFunction
函数功能: 周期性执行函数
输入参数: 消息
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2020/01/11
*************************************************/
void GpioControlPeriodNotifyFunction(Msg msg)
{
    ARMWorkModeControl();
    GpioLedControlHandle();
    LedBlinkedControlHandle();
    //to check the ECall LED
    CheckECallLEDState();
}

/*************************************************
函数名称: GpioLedControlUpdateStatusFunction
函数功能: 状态更新函数
输入参数: 消息
输出参数: 无
函数返回类型值：无
编写者: zxl
编写日期 :2017/02/16
*************************************************/
int GpioLedControlUpdateStatusFunction(GpioCtrlTypeEnum gpioCtrlType, GpioStatusTypeEnum gpioStatusType, uint8_t lightOnTimeCnt, uint8_t lightOffTimeCnt)
{
    if(gpioStatusType > GPIO_STATUS_INVALID)
    {
        SystemApiLogPrintf(LOG_ERROR_OUTPUT, "Gpio Update Status received INVALID gpioStatusType for gpioCtrlType=%d. \r\n", gpioCtrlType);
        return 1;
    }
    if (gpioStatusType == g_GpioControlInfoStruct[gpioCtrlType].curStatus)
    {
        return 0;
    }

    switch(gpioCtrlType)
    {
        case GPIO_CONTROL_CAN_RED_LED:
        case GPIO_CONTROL_CAN_GREEN_LED:
        case GPIO_CONTROL_MODULE_RED_LED:
        case GPIO_CONTROL_MODULE_GREEN_LED:
        case GPIO_CONTROL_GNSS_RED_LED:
        case GPIO_CONTROL_GNSS_GREEN_LED:
        case GPIO_CONTROL_ECALL_SOS_GREEN_LED:
        case GPIO_CONTROL_ECALL_SOS_RED_LED:
        {
            switch(gpioStatusType)
            {
                case GPIO_STATUS_OFF:
                {
                    g_GpioControlInfoStruct[gpioCtrlType].curStatus = gpioStatusType;
                    PwmChannelStop(g_GpioControlInfoStruct[gpioCtrlType].ioRemap);
                    break;
                }
                case GPIO_STATUS_ON:
                case GPIO_STATUS_BLINKED:
                case GPIO_STATUS_FLASH:
                {
                    g_GpioControlInfoStruct[g_GpioControlInfoStruct[gpioCtrlType].pairedCtrlType].curStatus= GPIO_STATUS_OFF;
                    PwmChannelStop(g_GpioControlInfoStruct[g_GpioControlInfoStruct[gpioCtrlType].pairedCtrlType].ioRemap);

                    g_GpioControlInfoStruct[gpioCtrlType].ioSetTime = lightOnTimeCnt;
                    g_GpioControlInfoStruct[gpioCtrlType].ioResetTime = lightOffTimeCnt;
                    g_GpioControlInfoStruct[gpioCtrlType].ioTimeRecord = 0x00;
                    g_GpioControlInfoStruct[gpioCtrlType].curStatus = gpioStatusType;
                    PwmChannelStart(g_GpioControlInfoStruct[gpioCtrlType].ioRemap);
                    break;
                }
                default:
                    g_GpioControlInfoStruct[gpioCtrlType].curStatus = gpioStatusType;
                    break;
            }
            break;
        }
        case GPIO_CONTROL_FDC_IO:
        {

            break;
        }
        case GPIO_CONTROL_GPS_PHY_LED:
        {

            break;
        }
        default:
            break;
    }


    return 0;
}

/*************************************************
函数名称: LedEventFunction
函数功能: LED事件功能函数
输入参数: 消息
输出参数: 无
函数返回类型值：无
编写者: liaoyonggang
编写日期 :2016/07/20
*************************************************/
void LedEventFunction(Msg msg)
{

    for(int index = 0; index < (sizeof(g_ledEventFunctionMap)/sizeof(g_ledEventFunctionMap[0])); index++)
    {
        if(g_ledEventFunctionMap[index].event == msg.event)
        {
            if(NULL != g_ledEventFunctionMap[index].TaskFunctionHook)
            {
                g_ledEventFunctionMap[index].TaskFunctionHook(msg);
            }
            break;
        }
    }
}

/*************************************************
函数名称: ECallStatusNotifyFunction
函数功能: ECall状态更新通知函数
输入参数: 消息
输出参数: msg
函数返回类型值：无
编写者: RichardChen
编写日期 :2024/04/12
*************************************************/
/*
static void ECallStatusNotifyFunction(Msg msg)
{
    EcallVoiceState  eCallStatus = ECALL_VOICE_STA_INVALID;
    uint8_t *para = (uint8_t*)msg.lparam;
    eCallStatus = (EcallVoiceState)para[0];
    
    switch(eCallStatus)
    {
        case ECALL_VOICE_STA_ACTIVE:
        {
            SystemApiLogPrintf(LOG_WARING_OUTPUT, "Received ECall status with call STARTED.\r\n");
            //to blink the GREEN LED
            if(GPIO_STATUS_BLINKED != g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED].curStatus)
            {
                g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED].curStatus = GPIO_STATUS_BLINKED;
                g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED].ioSetTime = GPIO_SOS_LED_GREEN_SET_TIME;
                g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED].ioResetTime = GPIO_SOS_LED_GREEN_RESET_TIME;
                g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED].ioTimeRecord = 0x00;
                GpioControlHandle(GPIO_CONTROL_ECALL_SOS_GREEN_LED, g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED]);

                //to turn OFF the RED LED
                //g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_RED_LED].curStatus = GPIO_STATUS_OFF;
                //g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_RED_LED].ioSetTime = GPIO_SOS_LED_RED_SET_TIME;
                //g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_RED_LED].ioResetTime = GPIO_SOS_LED_RED_RESET_TIME;
                //g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_RED_LED].ioTimeRecord = 0x00;
                //GpioControlHandle(GPIO_CONTROL_ECALL_SOS_GREEN_LED, g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED]);
            }
            break;
        }
        case ECALL_VOICE_STA_END:
        {
            SystemApiLogPrintf(LOG_WARING_OUTPUT, "Received ECall status with call ENDED.\r\n");
            //to turn ON the GREEN LED
            g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED].curStatus = GPIO_STATUS_ON;
            //g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED].ioSetTime = GPIO_SOS_LED_RED_SET_TIME;
            //g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED].ioResetTime = GPIO_SOS_LED_RED_RESET_TIME;
            //g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED].ioTimeRecord = 0x00;
            GpioControlHandle(GPIO_CONTROL_ECALL_SOS_GREEN_LED, g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED]);

            //Not to touch RED LED
            //g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_RED_LED].curStatus = GPIO_STATUS_ON;
            //g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_RED_LED].ioSetTime = GPIO_SOS_LED_RED_SET_TIME;
            //g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_RED_LED].ioResetTime = GPIO_SOS_LED_RED_RESET_TIME;
            //g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_RED_LED].ioTimeRecord = 0x00;
            //GpioControlHandle(GPIO_CONTROL_ECALL_SOS_RED_LED, g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_RED_LED]);
            break;
        }
        case ECALL_VOICE_STA_ALERTING:
        case ECALL_VOICE_STA_INCOMING:
        {
            SystemApiLogPrintf(LOG_WARING_OUTPUT, "Received ECall status with call status:%d.\r\n", eCallStatus);
            //to blink the GREEN LED
            g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED].curStatus = GPIO_STATUS_BLINKED;
            g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED].ioSetTime = GPIO_SOS_LED_GREEN_SET_TIME;
            g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED].ioResetTime = GPIO_SOS_LED_GREEN_RESET_TIME;
            g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED].ioTimeRecord = 0x00;
            GpioControlHandle(GPIO_CONTROL_ECALL_SOS_GREEN_LED, g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED]);

            //Not touch the RED LED
            //g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED].curStatus = GPIO_STATUS_OFF;
            //g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED].ioSetTime = GPIO_SOS_LED_RED_SET_TIME;
            //g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED].ioResetTime = GPIO_SOS_LED_RED_RESET_TIME;
            //g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED].ioTimeRecord = 0x00;
            //GpioControlHandle(GPIO_CONTROL_ECALL_SOS_GREEN_LED, g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED]);
            break;
        }
        default:
        {
            g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_RED_LED].curStatus = GPIO_STATUS_OFF;
            GpioControlHandle(GPIO_CONTROL_ECALL_SOS_RED_LED, g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_RED_LED]);

            //to turn ON the green LED　to indicate the ECall is available again
            g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED].curStatus = GPIO_STATUS_ON;
            GpioControlHandle(GPIO_CONTROL_ECALL_SOS_GREEN_LED, g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED]);
           
            SystemApiLogPrintf(LOG_WARING_OUTPUT, "ECall status with NOT supported state:%d, just turn ON green LED and turn OFF red LED\r\n", eCallStatus);
            break;
        }
    }
    
}
*/

/*************************************************
函数名称: ChangeLEDState
函数功能: 更新LED指示灯的状态
输入参数: ledIndex--指示灯的ID； state--指示灯的目标状态
输出参数: 无
函数返回类型值：无
编写者: RichardChen
编写日期 :2024/04/16
*************************************************/
void ChangeLEDState(uint16_t ledIndex, GpioLevel state)
{
#ifndef WINDOWS_SIM
    GpioSetOutputLevel(&g_gpioPowerOnInfoList[ledIndex], state);
#endif
}

/*************************************************
函数名称: CheckECallLEDState
函数功能: 检查和更新ECall LED指示灯的状态，防止ECall LED灯在IPC中断后意外常亮
输入参数: ledIndex--指示灯的ID； state--指示灯的目标状态
输出参数: 无
函数返回类型值：无
编写者: RichardChen
编写日期 :2024/04/16
*************************************************/
void CheckECallLEDState(void)
{
    if(ECALL_STATUS_ACTIVE == g_commonInfo.ecallStatus)
    {
        //check the IPC connection state if the ECall LED is blinking
        if(FLAG_NOT_RX_HEARTBEAT == g_ipcInfo.heartbeatFlag)
        {
            //turn OFF GREEN led
            g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED].curStatus = GPIO_STATUS_OFF;
            PwmChannelStop(g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_GREEN_LED].ioRemap);
            //blink the RED led
            g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_RED_LED].curStatus = GPIO_STATUS_BLINKED;
            PwmChannelStart(g_GpioControlInfoStruct[GPIO_CONTROL_ECALL_SOS_RED_LED].ioRemap);
            //to mark the global variable to prepare un-mute
            g_commonInfo.ecallStatus = ECALL_STATUS_INACTIVE;
//             ECallMuteControl();
        }
    }    
}