/*
      tbox_macro.h
描述：此文件主要是提供一些公共宏定义
作者：zhengyong
时间：2019.12.12
*/


#ifndef  __TBOX_MACRO_H__
#define  __TBOX_MACRO_H__
#ifndef WINDOWS_SIM
#include <unistd.h>
#endif

//1表示手工样 2表示OTS样 3表示NS样 4表示S样
#define PRODUCTION_STAGE         1
// 硬件版本号
#define HARDWARE_VERSION         "V1.0"
// 软件版本号
#define ARM_MAIN_VERION              1
#define ARM_SUB_VERION               9
#define ARM_MODIFY_VERSION           0


//定义该接口后，可以从GPS模块获取GPS标准输出格式数据，即NMEA-0183
#define NMEA_SUPPORT_GET_ORG_DATA

//该宏用于一些特殊的模块测试，如模拟接收can报文
#define SUPPORT_MODULE_TEST

//关闭宏时，RIL模块notify的短信和电话信号为原始数据
//打开时，则为组装后的数据，短信包括号码、时间和内容，电话则为来电状态
#define RIL_NOTIFY_DETAIL_STRUCT_DATA

//支持多路APN
#define SUPPORT_MULTI_APN

//PATH后面默认带'/'

//T-Box只读分区OEM根目录
#define TBOX_OEM_ROOT_PATH                "/mnt/oem/tbox/"

//T-Box调试模式标记，只读
#define TBOX_DEBUG_MODE_FLAG              TBOX_OEM_ROOT_PATH ".debugflag"

//T-Box应用根目录，会绑定到/mnt/oemdata/tbox 可读可写
#define TBOX_ROOT_PATH                    "/tbox/"

//T-Box可执行文件路径，/mnt/oem/tbox/bin的链接目录 内部文件可读可执行不可写
#define TBOX_EXCUTE_PATH                  TBOX_ROOT_PATH "bin/"

//全量CAN报文路径，如有emmc则为emmc分区3挂载点 可读可写
#define CAN_ASC_FILE_PATH                 TBOX_ROOT_PATH "can/"

//数据文件路径，如有emmc则为emmc分区2挂载点 可读可写
#define TBOX_DATA_ROOT_PATH               TBOX_ROOT_PATH "data/"

//缓存路径，如有emmc则为emmc分区2对应位置，可读可写
#define TBOX_DATA_CACHE_PATH              TBOX_DATA_ROOT_PATH "cache/"

//临时目录，临时大文件可使用，如有emmc则为emmc分区2对应位置，可读可写
#define CONFIG_SOIW_TMP_PATH              TBOX_DATA_ROOT_PATH "tmp/"
//系统临时目录，临时小文件可使用
#define CONFIG_SYSTEM_TMP_PATH            "/tmp/"

//日志路径，如有emmc则为emmc分区2对应位置，可读可写
#define CONFIG_SOIW_LOG_PATH              TBOX_DATA_ROOT_PATH "log/"

//pki路径，暂未使用
#define PKI_FILE_PATH                     TBOX_DATA_ROOT_PATH "P12/"
#define PKI_P12_FILE_PSD                   "rw.p12.psd"

//只读配置，/mnt/oem/tbox/etc_ro的链接目录 可读不可写
#define TBOX_CONFIG_RO_PATH               TBOX_ROOT_PATH "etc_ro/"

//可读写配置，实际存储在在/mnt/oemdata/tbox/etc_rw 可读可写
#define TBOX_CONFIG_RW_PATH               TBOX_ROOT_PATH "etc_rw/"

//T-Box动态库，/mnt/oem/tbox/lib的链接目录 可读可执行不可写
#define TBOX_SHARED_LIBARY_PATH           TBOX_ROOT_PATH "lib/"

//自升级路径，实际存储在/mnt/oemdata/tbox/ota 可读可写，ota读写需校验数据完整性和真实性
#define TBOX_OEM_UPGRADE_PATH             TBOX_ROOT_PATH "ota/"

//调试模式下的升级标记
#define TBOX_DEBUG_OTA_FLAG               TBOX_OEM_UPGRADE_PATH ".otaflag"

//run路径，用于存放pid或lock文件，复用系统目录
#define TBOX_RUN_PID_FILE_PATH            "/var/run/"

//脚本路径，/mnt/oem/tbox/sbin的链接目录 可读可执行不可写
#define TBOX_SCRIPT_PATH                  TBOX_ROOT_PATH "sbin/"

//web路径，量产时移除
#define CONFIG_SOIW_WEB_PATH              TBOX_ROOT_PATH "www/"

//第三方应用根目录，会绑定到/mnt/oemdata/opt 可读可写
#define TBOX_OPT_ROOT_PATH                TBOX_ROOT_PATH "usr/"

//第三方应用可执行程序目录
#define TBOX_OPT_EXCUTE_PATH              TBOX_OPT_ROOT_PATH "bin/"

//第三方应用数据目录
#define TBOX_OPT_DATA_PATH                TBOX_OPT_ROOT_PATH "data"

//第三方应用配置文件目录
#define TBOX_OPT_CONFIG_RW_PATH           TBOX_OPT_ROOT_PATH "etc/"

//第三方应用动态库目录
#define TBOX_OPT_LIBARY_PATH              TBOX_OPT_ROOT_PATH "lib"

//eMMC挂载根目录
#define EMMC_PARTITION_NUM    3                  // 分区数
#define EMMC_PARTITION_1_PATH TBOX_OPT_DATA_PATH // 分区1
#define EMMC_PARTITION_2_PATH CAN_ASC_FILE_PATH  // 分区2
#define EMMC_PARTITION_3_PATH TBOX_ROOT_PATH     // 分区3

//platform log相关
#define TBOX_PLATFORM_LOG_PATH            CONFIG_SOIW_LOG_PATH
#define TBOX_PLATFORM_LOG_FILE            "tbox_platform.log"
#define TBOX_PLATFORM_LOG_PATH_FILE       TBOX_PLATFORM_LOG_PATH TBOX_PLATFORM_LOG_FILE

//basic log相关
#define TBOX_BASIC_LOG_PATH               CONFIG_SOIW_LOG_PATH
#define TBOX_BASIC_LOG_FILE               "tbox_basic.log"
#define TBOX_BASIC_LOG_PATH_FILE          TBOX_BASIC_LOG_PATH TBOX_BASIC_LOG_FILE

//client log相关
#define TBOX_CLIENT_LOG_PATH              CONFIG_SOIW_LOG_PATH
#define TBOX_CLIENT_LOG_FILE              "tbox_client.log"
#define TBOX_CLIENT_LOG_PATH_FILE         TBOX_CLIENT_LOG_PATH TBOX_CLIENT_LOG_FILE

//mcu log相关
#define TBOX_MCU_LOG_PATH                 CONFIG_SOIW_LOG_PATH
#define TBOX_MCU_LOG_FILE                 "tbox_mcu.log"
#define TBOX_MCU_LOG_PATH_FILE            TBOX_MCU_LOG_PATH TBOX_MCU_LOG_FILE

//sync相关
#define TBOX_SYNC_LOG_PATH                CONFIG_SOIW_LOG_PATH
#define TBOX_SYNC_LOG_FILE                "tboxSync_log.log"
#define TBOX_SYNC_LOG_PATH_FILE           TBOX_SYNC_LOG_PATH TBOX_SYNC_LOG_FILE
#define TBOX_SYNC_BACK_PATH               TBOX_ROOT_PATH "back/"

// security log相关
#define TBOX_SECURITY_LOG_PATH             CONFIG_SOIW_LOG_PATH
#define TBOX_SECURITY_LOG_FILE             "tbox_security.log"
#define TBOX_SECURITY_LOG_PATH_FILE        (TBOX_SECURITY_LOG_PATH TBOX_SECURITY_LOG_FILE)

//MAC地址相关
#define CONFIG_SOIW_MAC_FILE              TBOX_CONFIG_RO_PATH "MADDR.conf"

//诊断仪升级相关
#define TBOX_OEM_DIAG_UPGRADE_PATH           TBOX_OEM_UPGRADE_PATH "diagUpgrade/"
#define TBOX_OEM_DIAG_UPGRADE_FILE           "upgrade.zip"
#define TBOX_OEM_DIAG_UPGRADE_PATH_FILE      TBOX_OEM_DIAG_UPGRADE_PATH TBOX_OEM_DIAG_UPGRADE_FILE
#define TBOX_OEM_DIAG_UPGRADE_SUCCESS_STAMP  TBOX_OEM_DIAG_UPGRADE_PATH "DiagUpgradeSuccessStamp"
#define TBOX_OEM_DIAG_UPGRADE_FAIL_STAMP     TBOX_OEM_DIAG_UPGRADE_PATH "DiagUpgradeFailStamp"

//ECU升级相关
#define ECUPGRADE_PATHINFO_PATH     TBOX_CONFIG_RO_PATH "ecupathinfo.xml"
#define ECUPGRADE_STEPINFO_PATH     TBOX_CONFIG_RO_PATH "ecuupgradestep.xml"

//安全校验相关
#define SECUIRITY_CONFIG_PATH       TBOX_CONFIG_RO_PATH "SecurityConfig.xml"
#define QC_ACTIVE_PROJECT 1
#define SYNLAND_ACTIVE_PROJECT 2

//公共宏定义
#define  SWITCH_ON                      1
#define  SWITCH_OFF                     0

#ifndef TRUE
#define TRUE 1
#endif

#ifndef FALSE
#define FALSE 0
#endif

#define  NORMAL_CMD_BUF_LEN             256
#define  NORMAL_CMD_OUT_BUF_LEN         256

#define DBUS_NAME_MAX_LENGTH            25
#define IPC_NOFITY_ACTION_MAX_LEN       DBUS_NAME_MAX_LENGTH

#define SMS_WAKEUP_WL_UNIT_LEN          20 //短信唤醒白名单单元长度
#define SMS_WAKEUP_WL_MAX_SIZE          20 //短信唤醒白名单列表长度

#ifndef WINDOWS_SIM
#define msleep(x) usleep(1000 * x)
#else
#define msleep(x) sleep(x)
#endif

#define PROPERTY_KEY_MAX_LEN            32 //包含结束符
#define PROPERTY_VALUE_MAX_LEN          128 //包含结束符

/************************升级文件路径***************************/
//升级临时文件路径
#define TBOX_OEM_UPGRADE_TMP_PATH                TBOX_OEM_UPGRADE_PATH "tmp/"

#define UPGRADE_PACKET_PATH                      TBOX_OEM_UPGRADE_PATH "upgrade.zip"
#define UPGRADE_INI_FILE_PATH                    TBOX_OEM_UPGRADE_TMP_PATH "iot.ini"
#define UPGRADE_ARM_FILE_PATH                    TBOX_OEM_UPGRADE_TMP_PATH ""
#define UPGRADE_ARM_SCRIPT_FILE_PATH             TBOX_OEM_UPGRADE_TMP_PATH ""
#define UPGRADE_BT_FILE_PATH                     TBOX_OEM_UPGRADE_TMP_PATH ""
#define UPGRADE_MCU_FILE_PATH                    TBOX_OEM_UPGRADE_TMP_PATH ""

#define UPGRADE_MCU_NEW_PATH                     TBOX_OEM_UPGRADE_PATH "mcu_new/"
#define UPGRADE_MCU_OLD_PATH                     TBOX_OEM_UPGRADE_PATH "mcu_old/"
#define UPGRADE_BT_NEW_PATH                      TBOX_OEM_UPGRADE_PATH "bt_new/"

//用于保存升级状态和升级结果
#define UPGRADE_RECORD_PATH                      TBOX_OEM_UPGRADE_PATH "upgradeRecord.ini"

/******************************* MCU pin Index *******************************/
typedef enum
{
    MCU_GPIO_AIRBAG_INPUT = 0,
    MCU_GPIO_REBOOT,
    MCU_GPIO_UART3_RX,
    MCU_GPIO_UART3_TX,
    MCU_GPIO_ARM_STATUS,
    MCU_GPIO_B_KEY_INPUT,
    MCU_GPIO_I_KEY_INPUT,
    MCU_GPIO_M_EXTAL,
    MCU_GPIO_M_XTAL,
    MCU_GPIO_IO_FWD_IO,
    MCU_GPIO_SOS_KEY_INPUT,
    MCU_GPIO_M_POWER_WIFI,
    MCU_GPIO_M_POWER_GNSSBACKUP,
    MCU_GPIO_M_EXTAL32,
    MCU_GPIO_M_XTAL32,
    MCU_GPIO_ACC,
    MCU_GPIO_M_RST_GNSS,
    MCU_GPIO_ARM_NET_STATUS_MCU,
    MCU_GPIO_BLE_STATUS_INT,
    MCU_GPIO_WAKEUP_ARM,
    MCU_GPIO_WHEEL_TICK_IO,
    MCU_GPIO_UART2_TX,
    MCU_GPIO_UART2_RX,
    MCU_GPIO_4G_LED_RED,
    MCU_GPIO_4G_LED_GREEN,
    MCU_GPIO_RTC_INT,
    MCU_GPIO_BLE_WAKEUP_CTL,
    MCU_GPIO_BLE_RST_CTL,
    MCU_GPIO_GNSS_ANT_SW,
    MCU_GPIO_CAN3_TX,
    MCU_GPIO_CAN3_RX,
    MCU_GPIO_RLIN21_TX,
    MCU_GPIO_RLIN21_RX,
    MCU_GPIO_BAT_VOLTAGE_DET,
    MCU_GPIO_MIC_VOLTAGE_DET,
    MCU_GPIO_BAT_NTC_DET,
    MCU_GPIO_RUN_VOLTAGE_DET,
    MCU_GPIO_M_TRANSLATOR_EN,
    MCU_GPIO_GNSS_ANT_OPEN,
    MCU_GPIO_GNSS_ANT_SHORT,
    MCU_GPIO_MAN_ANT_SW,
    MCU_GPIO_INFO_ARM_IO,
    MCU_GPIO_BAT_CHARGE_EN,
    MCU_GPIO_SENSOR_INT2,
    MCU_GPIO_SENSOR_INT1,
    MCU_GPIO_CAN3_STB,
    MCU_GPIO_CAN5_STB,
    MCU_GPIO_I2C_SCL,
    MCU_GPIO_I2C_SDA,
    MCU_GPIO_EN_VCC8V,
    MCU_GPIO_HANDSHAKE_LED_GREEN,
    MCU_GPIO_HANDSHAKE_LED_RED,
    MCU_GPIO_BATTERY_CUTOFF,
    MCU_GPIO_M_PWR_ONOFF,
    MCU_GPIO_GPS_LED_GREEN,
    MCU_GPIO_CAN2_TX,
    MCU_GPIO_CAN2_RX,
    MCU_GPIO_GPS_LED_RED,
    MCU_GPIO_CAN5_TX,
    MCU_GPIO_CAN5_RX,
    MCU_GPIO_CAN2_STB,
    MCU_GPIO_CAN1_STB,
    MCU_GPIO_4V1_PWR_SW,
    MCU_GPIO_M_ECALL_BTN_BL,
    MCU_GPIO_LTE_ANT_SHORT,
    MCU_GPIO_LTE_ANT_OPEN,
    MCU_GPIO_3V3_PWR_SW,
    MCU_GPIO_MUTE_OUTPUT,
    MCU_GPIO_CAN1_TX,
    MCU_GPIO_CAN1_RX,
    MCU_GPIO_ACC_OUT_CTL,
    MCU_GPIO_M_SOS_LED_RED,
    MCU_GPIO_RLIN21_SLP_N,
    MCU_GPIO_SCHG_STA_INPUT,
    MCU_GPIO_M_SOS_LED_GREEN,
    MCU_GPIO_M_SPI_CS,
    MCU_GPIO_M_SPI_CLK,
    MCU_GPIO_M_SPI_MISO,
    MCU_GPIO_M_SPI_MOSI,
    MCU_GPIO_FCHG_STA_INPUT,
    MCU_GPIO_M_WAKEUP_OUT,
    MCU_GPIO_ARM_SHDN_N,
    MCU_GPIO_M_SWDCLK,
    MCU_GPIO_ARM_RESET_MCU,
    MCU_GPIO_M_SWDIO,
    MCU_GPIO_ARM_RESET,
    MCU_GPIO_M_PRTRG_GNSS,
    MCU_MAX_NUM,
}McuGpioIndex;
typedef enum
{
    GPIO_OUTPUT_LOW = 0,
    GPIO_OUTPUT_HIGH,
    GPIO_DEFAULT_LEVEL,
}McuGpioLevel;

/* DID码 */
typedef enum {
    DID_TBOX_CFG_TYPE               = 0x0101, // 内部 TboxCfgType，每一个bit的详细解释见 TboxCfgType
    DID_UDS_STMIN                   = 0x0102, // 内部UDS STmin 时间
    DID_TBOX_ID                     = 0x1612, // TBOX 内部SN 五菱不使用

    DID_TBOX_SPARE_PART_NUMBER      = 0xF187, // 客户零件号
    DID_TBOX_SERIAL_NUMBER          = 0xF18C, // 客户SN
    DID_TBOX_SOFTWARE_ID            = 0xF188, // ECU软件编号
    DID_TBOX_HARDWARE_ID            = 0xF191, // ECU硬件编号
    DID_TBOX_SUPPIER_IDENTIFIER     = 0xF18A, // 供应商代码
    DID_VIN_CODE                    = 0xF190, // VIN 码
    DID_TBOX_HARD_VERSION           = 0xF193, // 硬件版本号
    DID_TBOX_SOFT_VERSION           = 0xF195, // 软件版本号
    DID_TBOX_NAME                   = 0xF197, // ECU NAME
    DID_SOFT_UPDATE_DATE            = 0xF199, // 软件更新日期 7 位 BCD
    DID_VEHICLE_OFFLINE_CONFIG      = 0xF110, // 车辆下线配置

    DID_TUID                        = 0x1600, // TUID，T-Box SN，32 位 ASCII 后20字节是SN 对应0xF18C
    DID_TBOX_AUTH_STATUS            = 0x1601, // TBOX 登签认证状态
    DID_SIM_NUMBER                  = 0x1602, // SIM 卡号
    DID_ICCID                       = 0x1603, // ICCID 号
    DID_IMEI                        = 0x1604, // IMEI
    DID_TBOX_CERTIFICATE_ID         = 0x1605, // TBOX 数字证书编号
    DID_LINK1_ADDR                  = 0x1606, // TBOX 第 1 链路地址
    DID_LINK1_PORT                  = 0x1607, // TBOX 第 1 链路地址端口
    DID_LINK2_ADDR                  = 0x1608, // TBOX 第 2 链路地址
    DID_LINK2_PORT                  = 0x1609, // TBOX 第 2 链路地址端口
    DID_LINK3_ADDR                  = 0x1610, // TBOX 第 3 链路地址
    DID_LINK3_PORT                  = 0x1611, // TBOX 第 3 链路地址端口
    DID_THIRD_PARTY_LINK_ADDR       = 0x1613, // 第三方平台链路地址
    DID_THIRD_PARTY_LINK_PORT       = 0x1614, // 第三方平台链路地址
    DID_WAKEUP_TIME_INTERVAL        = 0x1615, // 定时唤醒间隔，默认 4h
    DID_LINK3_ADDR_USERNAME         = 0x1616, // TBOX 第 3 链路登录名
    DID_LINK3_PORT_PASSWORD         = 0x1617,  // TBOX 第 3 链路登录密码
    DID_AUTO_RECHARGE_UNDER_VOLTAGE = 0x1618, // 智能补电欠压阈值
    DID_LINK1_SSL_ENABLE            = 0x1619, // 第一链路 SSL 使能
    DID_LINK2_SSL_ENABLE            = 0x1620, // 第二链路 SSL 使能
    DID_OFFLINE_CHECK_FLAG          = 0x1621, // 是否触发下线检测
    DID_AC_TIMEOUT_MINUTES          = 0x1622, // 空调运行超时关闭时间（单位为分钟）
    DID_TIMED_WAKEUP_ENABLE         = 0x1623, // 定时唤醒使能
    DID_NETWORK_WAKEUP_ENABLE       = 0x1624, // 网络唤醒使能
    DID_GLOBAL_LOG_ENABLE           = 0x1625, // 全局日志输出使能
    DID_LINK1_ENABLE                = 0x1626, // 第一链路使能
    DID_LINK2_ENABLE                = 0x1627, // 第二链路使能
    DID_LINK3_ENABLE                = 0x1628, // 第三链路使能
    DID_THIRD_PARTY_LINK_ENABLE     = 0x1629, // 第三方平台链路使能
    DID_CERT_UPDATE_TIME            = 0x1630, // 证书更新时间
    DID_DATA_RESEND_TEST_TIME       = 0x1631, // 数据补发测试时间
    DID_LEVEL3_ALARM_TEST_TIME      = 0x1632, // 3 级报警测试时间
    // DID_VEHICLE_RESTRICT_FLAG       = 0x1633, // 限速或锁车标志 (已废弃)
    DID_LINK3_BI_AUTH_PORT          = 0x1634, // 第3链路双向认证端口
    DID_SPEED_LIMIT_VALUE           = 0x1635, // 限速值 0~255
    DID_LOCK_CAR_STATUS             = 0x1636, // 锁车状态 (00:无命令, 01:进入锁车, 02:退出锁车)
    DID_SPEED_LIMIT_STATUS          = 0x1637, // 限速状态 (00:无命令, 01:进入限速, 02:退出限速)

    //动态DID
    DID_ECU_BATTERY_VOLTAGE         = 0xCF00, // 电池电压 (ECU供电电压)
    DID_VEHICLE_SPEED               = 0xCF01, // 车速
    DID_ODOMETER                    = 0xCF02, // 里程
    DID_LOW_POWER_MODE              = 0xCF03, // 低压电源模式
    DID_DATE_AND_TIME               = 0xCF04, // 日期和时间
    DID_VEHICLE_POWER_MODE          = 0xCF05, // 整车动力模式
    DID_GEAR_POSITION               = 0xCF06, // 档位
    DID_CELLULAR_SIGNAL_STRENGTH    = 0x1650, // 移动网络信号强度
    DID_GNSS_SIGNAL_STRENGTH        = 0x1651, // GNSS信号强度
    DID_WIFI_SIGNAL_STRENGTH        = 0x1652, // WIFI信号强度
    DID_LINK1_TCP_STATUS            = 0x1653, // 第1链路TCP连接状态
    DID_LINK2_TCP_STATUS            = 0x1654, // 第2链路TCP连接状态
    DID_LINK3_TCP_STATUS            = 0x1655, // 第3链路TCP连接状态
    DID_POWER_MANAGEMENT_MODE       = 0x1656, // 电源管理工作模式
    DID_MILEAGE_CLEAR_COUNT         = 0x1660, // 里程清零计数
    DID_TBOX_SAVE_MILEAGE_VALUE     = 0x1661, // TBOX保存里程值
    DID_RDS_SOFTWARE_IN             = 0x1670, //远程诊断软件版本号内部
    DID_RDS_SOFTWARE_OUT            = 0x1672, //远程诊断软件版本号外部
    DID_RDS_SUPPIER_IDENTIFIER      = 0x1671, //远程诊断软件版本号内部

    DID_CAR_INFO_DID                = 0xFFC0,
    DID_STATIC_DID                  = 0xFFC1,
    DID_STATIC2_DID                 = 0xFFC2,
    DID_DYNAMIC_DID                 = 0xFFC3,
    DID_TOTAL_DID                   = 0xFFCC,

    //控制类DID
    DID_CTRL_MCU_GPIO               = 0xFCC0, //控制mcu引脚，两字节，[McuGpioIndex,McuGpioLevel]
}DidTypeInfo_en;

/******************** DID length **********************/
#define IMEI_LEN                            15  // IMEI
#define ICCID_LEN                           20  // ICCID
#define VIN_CODE_LEN                        17  // VINCODE

//代码固化DID信息
#define TBOX_SOFTWARE_ID_LEN                      10
#define TBOX_HARDWARE_ID_LEN                      10
#define TBOX_SYSTEM_SUPPIER_IDENTIFIER_LEN        10
#define TBOX_NAME_LEN                             10

#define TBOX_SOFT_VERSION_LEN                    8
#define TBOX_HARD_VERSION_LEN                    8
#define TBOX_BOOT_VERSION_LEN                    8

//DID结构体参数长度定义
#define TBOX_SPARE_PARTNUMBERMAX                         10 // 客户零件号最大长度，表示客户零件号的最大字符数
#define TBOX_VIN_NUM                                     17 // VIN码的长度，表示车辆识别码的字符数
#define TBOX_SOFTWARE_UPDATE_DATE_LEN                    7  // 软件更新日期的长度，7位BCD格式的日期
#define VEHICLE_OFFLINE_CONFIG_LEN                       8  // 车辆下线配置长度，表示车辆下线时配置的字节数

#define TUID_LEN                                         32  // TUID的长度，表示T-Box的唯一标识符，32字节ASCII码
#define TBOX_SERIAL_NUMBER_LEN                           20  // 序列号的长度，TUID后20字节
#define TBOX_SIM_LEN                                     13  // SIM卡号的长度，表示SIM卡的数字字符数
#define TBOX_ICCID_LEN                                   20  // ICCID号的长度，表示SIM卡的ICCID编号字符数
#define TBOX_IMEI_LEN                                    15  // IMEI号的长度，表示手机或设备的国际移动设备身份码字符数
#define TBOX_CERT_LEN                                    32  // TBOX证书编号的长度，表示TBOX数字证书的编号字符数
#define TBOX_LINK_LEN                                    32  // 链路地址的长度，表示TBOX用于通信的链路地址长度
#define TBOX_LINK_PORT_LEN                               8   // 链路端口的长度，表示TBOX链路端口的字符数
#define TBOX_ID                                          10  // TBOX内部SN的长度，表示TBOX设备的内部编号长度

#define TBOX_LINK_USERNAME_LEN                           64  // 链路用户名的长度，表示用于链路连接的用户名最大长度
#define TBOX_LINK_PASSWORD_LEN                           64  // 链路密码的长度，表示用于链路连接的密码最大长度

#define CERT_UPDATE_TIME                                 7   // 证书更新时间的长度，表示更新证书的时间长度，7字节
//部分动态DID长度
#define RDS_SOFTWARE_IN_LEN  20  // OTAmaster、远程诊断软件版本号内部长度
#define RDS_SOFTWARE_OUT_LEN 10  // OTAmaster、远程诊断软件版本号外部长度
#define RDS_SOFTWARE_SUPPLIER_IDENTIFIER_LEN 10  // OTAmaster、远程诊断软件版本供应商标识长度

/*******************汽车产线信息***********************/
#define TBOX_MAX_CONFIG_PARA_LEN            128
#define TSP_DOMAIN_CONFIG_PARA_LEN          50
#define TSP_VERSION_CONFIG_PARA_LEN         10
#define TBOX_VIN_MAX_LEN                    17
#define TBOX_CAN_PERIOD_NAME                200
#define CAN_FRAME_DATA_COUNT                8
#define GENERAL_ICCID_LENGTH                20


#define UPGRADE_CLIENT_PASSWORD_LEN         6
#define UPGRADE_CLIENT_ARM_FILE_LEN         64
#define UPGRADE_CLIENT_SCRIPT_COUNT         5
#define UPGRADE_CLIENT_FILE_COUNT           5

#define MCU_BOOT_UPGRADE_VERSION            0xffff

//ARM从业务激活状态转换成非激活状态的静默时间范围
#define ARM_INACTIVE_SILENT_MIN_TIME        5   //s
#define ARM_INACTIVE_SILENT_MAX_TIME        120 //s


/*********周期处理时间间隔默认值**********/
#define  TBOX_ARM_STATUS_REPORT_PERIOD          10     //10S
#define  TBOX_ARM_INTEGRAL_TIME_PERIOD          3600   //整点校时间隔时间3600s(即一小时)

/*********CAN报文极值信息**********/
#define  CAN_MSG_MAX_LEN                     8

//自动拨号相关信息
#define AUTO_APN_TECH             APP_TECH_UMTS
#define AUTO_APN_NAME             "auto"
#define DNS_SERVER_ADDRESS_0      "*******"
#define DNS_SERVER_ADDRESS_1      "**********"
#define DNS_SERVER_ADDRESS_2      "************"
#define DNS_CONFIG_FILE_PATH      "/etc/resolv.conf"
#define AUTO_APN_INTERFACE_NAME   "rmnet_data5"   //EC20的网口是对应的，profile1~6对应rmnet_data0~5

//定时任务相关信息
#define TIMER_TASK_TOTAL_MAX       8
#define TIMER_TASK_NAME_MAX        25
#define TIMER_TASK_PARA_MAX        25

#endif

