#include <errno.h>
#include <fcntl.h>
#include <pthread.h>
#include <stdarg.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <signal.h>
#include <unistd.h>
#include <sys/socket.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <sys/un.h>
#include <sys/shm.h>
#include "property_api.h"
#include "SystemApi.h"
#include "iot_gps_logic.h"
#include "iot_logc.h"
#include "iot_gpsc.h"
#include "iot_dbus.h"
#include "tbox_error.h"

/*
 * @brief 异步执行的ACTION
 */
typedef enum
{
    GPS_ACTION_IDLE = 0,
    GPS_ACTION_START,
    GPS_ACTION_STOP,
    GPS_ACTION_AGPS,
} GpsAction;
// 当前执行动作
static GpsAction g_CurrentAction = GPS_ACTION_START;
static GpsModuleType g_GpsType = GPS_MODULE_INSIDE;

#define GPS_HANDLE_MSG_TIME_OUT 50 //ms

static boolean g_keepWork = TRUE;

static void GpsSigintHandler(int sig)
{
    LogServiceBuffer(TBOX_MODULE_GPS, TBOX_LOG_WARN, "Gps service received signal(%d).", sig);
    switch (sig) {
        case SIGINT:
        case SIGKILL:
        case SIGTERM:
            g_keepWork = FALSE;
            break;
        case 40:
            SetNmeaPrintFlag(0);
            SetIntProperty(GPS_DATA_PRINT_FLAG_KEY, 0);
            LogServiceBuffer(TBOX_MODULE_GPS, TBOX_LOG_WARN, "close nema print");
            break;
        case 41:
            SetNmeaPrintFlag(1);
            SetIntProperty(GPS_DATA_PRINT_FLAG_KEY, 1);
            LogServiceBuffer(TBOX_MODULE_GPS, TBOX_LOG_WARN, "open nema print.");
            break;
    }
}

/*************************************************
函数名称: GpsStart
函数功能: 启动GPS服务
输入参数: msgPara - 未使用
输出参数: msgReturn - 是否启动成功
返回值：无
编写者: zhengyong
编写日期 :2019/12/30
*************************************************/
void GpsStart(DBusMsgParaInfo_s *msgPara, DBusMsgParaInfo_s *msgReturn)
{
    (void)msgPara;

    if (NULL == msgReturn)
    {
        LogServiceBuffer(TBOX_MODULE_GPS, TBOX_LOG_ERROR, "Null pointer in %s.", __FUNCTION__);
        return;
    }

    msgReturn->type = TBOX_DBUS_TYPE_INT32;
    // 移柯内置GNSS创建单独线程异步处理Action
    if(GPS_MODULE_INSIDE == g_GpsType)
    {
        g_CurrentAction = GPS_ACTION_START;
        msgReturn->typeInfo.i32 = TBOX_OK;
    }
    else
    {
        msgReturn->typeInfo.i32 = GpsStartNavigat();
    }

    LogServiceBuffer(TBOX_MODULE_GPS, TBOX_LOG_DEBUG, "Start gps success in gpsd.");
}

/*
 * @brief 启动AGPS
 * @param msgPara - 未使用
 * @param msgReturn - 是否启动成功
 */
void GpsStartAgps(DBusMsgParaInfo_s *msgPara, DBusMsgParaInfo_s *msgReturn)
{
    (void)msgPara;

    if (NULL == msgReturn)
    {
        LogServiceBuffer(TBOX_MODULE_GPS, TBOX_LOG_ERROR, "Null pointer in %s.", __FUNCTION__);
        return;
    }
    msgReturn->type = TBOX_DBUS_TYPE_INT32;
    // 移柯内置GNSS创建单独线程异步处理Action
    if(GPS_MODULE_INSIDE == g_GpsType)
    {
        g_CurrentAction = GPS_ACTION_AGPS;
        msgReturn->typeInfo.i32 = TBOX_OK;
    }
    else
    {
        msgReturn->typeInfo.i32 = GpsAgpsNavigat();
    }

    LogServiceBuffer(TBOX_MODULE_GPS, TBOX_LOG_DEBUG, "Check agps success in gpsd.");

}

/*************************************************
函数名称: GpsStop
函数功能: 停止GPS服务
输入参数: msgPara - 未使用
输出参数: msgReturn - 是否停止成功
返回值：无
编写者: zhengyong
编写日期 :2019/12/30
*************************************************/
void GpsStop(DBusMsgParaInfo_s *msgPara, DBusMsgParaInfo_s *msgReturn)
{
    (void)msgPara;

    if (NULL == msgReturn)
    {
        LogServiceBuffer(TBOX_MODULE_GPS, TBOX_LOG_ERROR, "Null pointer in %s.", __FUNCTION__);
        return;
    }

    msgReturn->type = TBOX_DBUS_TYPE_INT32;
    // 移柯内置GNSS创建单独线程异步处理Action
    if(GPS_MODULE_INSIDE == g_GpsType)
    {
        g_CurrentAction = GPS_ACTION_STOP;
        msgReturn->typeInfo.i32 = TBOX_OK;
    }
    else
    {
        msgReturn->typeInfo.i32 = GpsStopNavigat();
    }

    LogServiceBuffer(TBOX_MODULE_GPS, TBOX_LOG_DEBUG, "Stop gps success in gpsd.");
}

/*************************************************
函数名称: GpsCalibrate
函数功能: GPS校准?
输入参数: msgPara - 未使用
输出参数: msgReturn - 是否校准成功
返回值：无
编写者: zhengyong
编写日期 :2019/12/30
*************************************************/
void GpsCalibrate(DBusMsgParaInfo_s *msgPara, DBusMsgParaInfo_s *msgReturn)
{
    (void)msgPara;

    if (NULL == msgReturn)
    {
        LogServiceBuffer(TBOX_MODULE_GPS, TBOX_LOG_ERROR, "Null pointer in %s.", __FUNCTION__);
        return;
    }

    msgReturn->type = TBOX_DBUS_TYPE_INT32;
    msgReturn->typeInfo.i32 = GpsCalibrateNavigat();

    LogServiceBuffer(TBOX_MODULE_GPS, TBOX_LOG_DEBUG, "Calibrate gps success in gpsd.");
}

/*************************************************
函数名称: GpsGetLocation
函数功能: 获取GPS定位信息
输入参数: msgPara - 未使用
输出参数: msgReturn - 定位数据信息或错误 码
返回值：无
编写者: zhengyong
编写日期 :2019/12/30
*************************************************/
void GpsGetLocation(DBusMsgParaInfo_s *msgPara, DBusMsgParaInfo_s *msgReturn)
{
    int ret =  TBOX_OK;
    GpsInfo *gpsInfo = NULL;
    (void)msgPara;

    if (NULL == msgReturn)
    {
        LogServiceBuffer(TBOX_MODULE_GPS, TBOX_LOG_ERROR, "Null pointer in %s.", __FUNCTION__);
        printf("Null pointer in %s.", __FUNCTION__);

    }

    gpsInfo = (GpsInfo *)malloc(sizeof(GpsInfo));
    DBUS_CHECK_NULL_RESULT_NON_RETURN(TBOX_MODULE_GPS, gpsInfo, msgReturn, TBOX_ERROR_MALLOC_FAIL);

    memset(gpsInfo, 0, sizeof(GpsInfo));
    ret = GpsGetLocInfo(gpsInfo);
    if (TBOX_OK != ret)
    {
        LogServiceBuffer(TBOX_MODULE_GPS, TBOX_LOG_ERROR, "Get gps location failed in gpsd.");
        msgReturn->type = TBOX_DBUS_TYPE_INT32;
        msgReturn->typeInfo.i32 = ret;
        free(gpsInfo);
        gpsInfo=NULL;
        return;
    }

    msgReturn->type = TBOX_DBUS_TYPE_ARRAY;
    msgReturn->arrayLen = sizeof(GpsInfo);
    msgReturn->arrayType = TBOX_DBUS_TYPE_UINT8;
    msgReturn->typeInfo.array = gpsInfo;
}

const static DBusMsgHandleInfo_s g_gpsHandleInfo[] =
{
    {DBUS_MODULE_GPS, DBUS_ACTION_START,        GpsStart},
    {DBUS_MODULE_GPS, DBUS_ACTION_STOP,         GpsStop},
    {DBUS_MODULE_GPS, DBUS_ACTION_CALIBRATE,    GpsCalibrate},
    {DBUS_MODULE_GPS, DBUS_ACTION_GET_LOCATION, GpsGetLocation},
    {DBUS_MODULE_GPS, DBUS_ACTION_AGPS,         GpsStartAgps},
    {NULL, NULL, NULL},
};

/*
 * @brief 异步处理GPS Actions
 * @param 无
 * @return 无
 * @note 移柯部分GNSS接口，耗时较长，只在内置GNSS时，start stop agps异步执行
 */
void *GpsActionHandler(void *args)
{
    while(g_keepWork)
    {
        switch(g_CurrentAction)
        {
            case GPS_ACTION_START:
            {
                if (GpsStartNavigat() != TBOX_OK)
                {
                    GpsDeinitNavigat();
                    LogServiceBuffer(TBOX_MODULE_GPS, TBOX_LOG_ERROR, "GPS Start failed");
                    g_keepWork = FALSE;
                }
                g_CurrentAction = GPS_ACTION_IDLE;
                break;
            }
            case GPS_ACTION_STOP:
            {
                GpsStopNavigat();
                g_CurrentAction = GPS_ACTION_IDLE;
                break;
            }
            case GPS_ACTION_AGPS:
            {
                GpsAgpsNavigat();
                g_CurrentAction = GPS_ACTION_IDLE;
                break;
            }
            case GPS_ACTION_IDLE:
            default:
            {
                msleep(GPS_HANDLE_MSG_TIME_OUT);
            }
        }
    }
    LogServiceBuffer(TBOX_MODULE_GPS, TBOX_LOG_WARN, "Gps Action Tread Exit.");
}

/*
 * @brief 创建GNSS异步处理线程
 * @param 无
 * @return 0: 成功 其它: 失败
 */
static int CreatGpsActionHandler(void)
{
    int ret;
    pthread_t tid;
    pthread_attr_t attr;

    pthread_attr_init(&attr);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);
    ret = pthread_create(&tid, &attr, GpsActionHandler, NULL);
    if (ret)
    {
        LogServiceBuffer(TBOX_MODULE_GPS, TBOX_LOG_ERROR, "Gps Action Tread Creat Err: %s", strerror(errno));
    }
    else
    {
        LogServiceBuffer(TBOX_MODULE_GPS, TBOX_LOG_INFO, "Gps Action Tread Creat Success");
    }
    return ret;
}

int main(int argc, char *argv[])
{
    int ret;

    signal(SIGINT, GpsSigintHandler);
    signal(SIGKILL, GpsSigintHandler);
    signal(SIGTERM, GpsSigintHandler);
    signal(40, GpsSigintHandler);
    signal(41, GpsSigintHandler);

    if(daemon(0, 0)){}

    LogServiceBuffer(TBOX_MODULE_GPS, TBOX_LOG_INFO, "===TBOX GPS SERVICE START===");

    InitMmProperty();

    g_GpsType = GetGpsModuleType();
    if (GpsPowerOnInit(g_GpsType) != TBOX_OK)
    {
        LogServiceBuffer(TBOX_MODULE_GPS, TBOX_LOG_WARN, "GPS PowerOn Init failed: %d", ret);
        return -1;
    }

    ret = DBusCreateConnection(DBUS_SERVICE_GPS_NAME);
    if (TBOX_OK != ret)
    {
	    LogServiceBuffer(TBOX_MODULE_GPS, TBOX_LOG_ERROR, "Create dbus connection failed in GPS, ret=%d.", ret);
	    return TBOX_ERROR;
    }
    // 移柯内置GNSS创建单独线程异步处理Action
    if (g_GpsType == GPS_MODULE_INSIDE)
    {
        CreatGpsActionHandler();
    }

    while(g_keepWork)
    {
	    msleep(GPS_HANDLE_MSG_TIME_OUT);
	    DBusHandleMessage(g_gpsHandleInfo, 0);
    }

    DBusDestroyConnection();
    GpsStopNavigat();
    GpsDeinitNavigat();

    LogServiceBuffer(TBOX_MODULE_GPS, TBOX_LOG_INFO, "===TBOX GPS SERVICE STOP===");

}

