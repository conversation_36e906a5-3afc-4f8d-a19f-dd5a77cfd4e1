#include <string.h>

#include "iot_dbus.h"
#include "iot_gpsc.h"
#include "iot_logc.h"
#include "iot_gps_logic.h"

#define GPS_SEND_MSG_TIME_OUT 1000 //ms


/*
 * 函数功能：开启GPS定位的功能
 * 输入参数：无
 * 输出参数：无
 * 函数返回类型值：0，正常；-1，错误
 * 函数说明：该函数开启GPS的定位，在电源管理的唤醒函数中会默认调用该函数
 */
int StartGpsNavigat(void)
{
    int ret = TBOX_OK;

    LogServiceBuffer(TBOX_MODULE_GPSC, TBOX_LOG_DEBUG, "Start gps in gpsc.");

    ret = SetMessageByDBus(DBUS_SERVICE_GPS_NAME, DBUS_MODULE_GPS, DBUS_ACTION_START, NULL, GPS_SEND_MSG_TIME_OUT);
    if (TBOX_OK != ret)
    {
        LogServiceBuffer(TBOX_MODULE_GPSC, TBOX_LOG_ERROR, "Start gps failed, ret=%d.", ret);
    }

    return ret;
}

/*
 * 函数功能：结束GPS定位的功能
 * 输入参数：无
 * 输出参数：无
 * 函数返回类型值：0，正常；-1，错误
 * 函数说明：该函数关闭GPS的定位，同时具备节能的效果，在电源管理函数中会默认调用该函数进入省电状态
 */
int StopGpsNavigat(void)
{
    int ret = TBOX_OK;

    LogServiceBuffer(TBOX_MODULE_GPSC, TBOX_LOG_DEBUG, "Stop gps in gpsc.");

    ret = SetMessageByDBus(DBUS_SERVICE_GPS_NAME, DBUS_MODULE_GPS, DBUS_ACTION_STOP, NULL, GPS_SEND_MSG_TIME_OUT);
    if (TBOX_OK != ret)
    {
        LogServiceBuffer(TBOX_MODULE_GPSC, TBOX_LOG_ERROR, "Stop gps failed, ret=%d.", ret);
    }

    return ret;
}

/*
 * 函数功能：开启AGPS功能
 * 输入参数：无
 * 输出参数：无
 * 函数返回类型值：0，正常；-1，错误
 * 函数说明：该函数用于在网络拨号成功后通知GPS检查并注入AGPS功能
 */
int StartAgpsNavigat(void)
{
    int ret = TBOX_OK;

    LogServiceBuffer(TBOX_MODULE_GPSC, TBOX_LOG_DEBUG, "Start agps in gpsc.");

    ret = SetMessageByDBus(DBUS_SERVICE_GPS_NAME, DBUS_MODULE_GPS, DBUS_ACTION_AGPS, NULL, GPS_SEND_MSG_TIME_OUT);
    if (TBOX_OK != ret)
    {
        LogServiceBuffer(TBOX_MODULE_GPSC, TBOX_LOG_ERROR, "Start agps failed, ret=%d.", ret);
    }

    return ret;
}

/*
 * 函数功能：获取GPS的定位数据
 * 输入参数：nmeaInfo
 * 输出参数：无
 * 函数返回类型值：0，正常；-1，错误
 * 函数说明：该函数可以被并发的访问
 */
int GetGpsLocInfo(GpsInfo *nmeaInfo)
{
    int ret = TBOX_OK;
    DBusMsgParaInfo_s returnInfo;

    LogServiceBuffer(TBOX_MODULE_GPSC, TBOX_LOG_DEBUG, "Get gps location in gpsc.");

    TBOX_CHECK_NULL_RETURN(TBOX_MODULE_GPSC, nmeaInfo, TBOX_ERROR_NULL_POINTER);

    ret = GetMessageByDBus(DBUS_SERVICE_GPS_NAME, DBUS_MODULE_GPS, DBUS_ACTION_GET_LOCATION, &returnInfo, GPS_SEND_MSG_TIME_OUT);
    if (TBOX_OK != ret)
    {
        LogServiceBuffer(TBOX_MODULE_GPSC, TBOX_LOG_ERROR, "Send gps action failed, ret=%d.", ret);
        return ret;
    }

    switch (returnInfo.type)
    {
        case TBOX_DBUS_TYPE_ARRAY:
            memcpy((void *)nmeaInfo, returnInfo.typeInfo.array, sizeof(GpsInfo));
            DBusFreeMessageInfo(&returnInfo);
            break;
        case TBOX_DBUS_TYPE_INT32:
            LogServiceBuffer(TBOX_MODULE_GPSC, TBOX_LOG_ERROR, "Remote return error(%d) when get gps locatoin.", returnInfo.typeInfo.i32);
            return returnInfo.typeInfo.i32;
        default:
            LogServiceBuffer(TBOX_MODULE_GPSC, TBOX_LOG_ERROR, "Remote return wrong result type(%c) when get gps locatoin.", returnInfo.type);
            if(TBOX_DBUS_TYPE_STRING == returnInfo.type)
            {
                LogServiceBuffer(TBOX_MODULE_GPSC, TBOX_LOG_ERROR, "Error msg:%s.", returnInfo.typeInfo.str);
                DBusFreeMessageInfo(&returnInfo);
            }
            return DBUS_ERROR_REMOTE_WRONG_TYPE;
    }

    LogServiceBuffer(TBOX_MODULE_GPSC, TBOX_LOG_DEBUG, "Get gps location success in gpsd, inuse=%u, inview=GPS:%u BD:%u GL:%u GA%u, "
                "lat=%lf, lon=%lf, min:%u, sec: %u, fix:%u", nmeaInfo->satinfo.inuse, nmeaInfo->satinfo.gpsinview, nmeaInfo->satinfo.bdinview, nmeaInfo->satinfo.glinview, nmeaInfo->satinfo.gainview,
                nmeaInfo->lat, nmeaInfo->lon, nmeaInfo->utc.min, nmeaInfo->utc.sec, nmeaInfo->fix);
    return TBOX_OK;
}

int CalibrateGpsNavigat(void)
{
    int ret = TBOX_OK;

    LogServiceBuffer(TBOX_MODULE_GPSC, TBOX_LOG_DEBUG, "Calibrate gps navigat in gpsc.");

    ret = SetMessageByDBus(DBUS_SERVICE_GPS_NAME, DBUS_MODULE_GPS, DBUS_ACTION_CALIBRATE, NULL, GPS_SEND_MSG_TIME_OUT);
    if (TBOX_OK != ret)
    {
        LogServiceBuffer(TBOX_MODULE_GPSC, TBOX_LOG_ERROR, "Calibrate gps failed, ret=%d.", ret);
    }

    return ret;
}


/*************************************************
函数名称: GetGpsModuleType
函数功能: 从配置文件读取当前gps定位模式（内置/外置）
输入参数: 无
输出参数: 值
函数返回类型值：无
编写者: Young
编写日期 :2018/07/18
*************************************************/
uint8_t  GetGpsModuleType(void)
{
    uint8_t ret = GPS_MODULE_INSIDE;


    //TODO 读取配置文件中设置的定位模式

    if(ret == GPS_MODULE_EXTERN)
    {
        return GPS_MODULE_EXTERN;	// 外置惯导模块
    }
    else if(ret == GPS_MODULE_INSIDE)
    {
        return GPS_MODULE_INSIDE;		// 内置定位模块
    }
    else
    {
        return GPS_MODULE_DEFAULT;		// 默认外置模块
    }
}


/*************************************************
函数名称: SetGpsModuleType
函数功能: 设置配置文件读取当前gps定位模式（内置/外置）
输入参数: 无
输出参数: 值
函数返回类型值：无
编写者: Young
编写日期 :2018/07/18
*************************************************/
uint8_t SetGpsModuleType(uint8_t type)
{
    uint8_t ret = 0x00;

    //TODO 设置配置文件中设置的定位模式
}


